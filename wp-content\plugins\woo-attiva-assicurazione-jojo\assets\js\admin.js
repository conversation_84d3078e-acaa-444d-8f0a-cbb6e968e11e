/**
 * JavaScript per la gestione delle interazioni nell'admin
 */
jQuery(document).ready(function($) {
    
    /**
     * Gestione form di registrazione assicurazione
     */
    /*
    $('#waa-register-insurance-form').on('submit', function(e) {
        e.preventDefault();
        
        const form = $(this);
        const messageContainer = $('#waa-message');
        
        // Mostro messaggio di caricamento
        messageContainer.attr('class', 'waa-message').show().html('Registrazione in corso...');
        
        // Recupero dati form
        const formData = new FormData(form[0]);
        formData.append('action', 'waa_register_insurance');
        formData.append('nonce', waa_params.nonce);
        
        // Invio richiesta AJAX
        $.ajax({
            url: waa_params.ajax_url,
            type: 'POST',
            data: formData,
            processData: false,
            contentType: false,
            success: function(response) {
                if (response.success) {
                    messageContainer.attr('class', 'waa-message success').html(response.data.message);
                    
                    // Resetto il form
                    form[0].reset();
                    
                    // Ricarico la pagina se richiesto
                    if (response.data.reload) {
                        setTimeout(function() {
                            location.reload();
                        }, 1500);
                    }
                } else {
                    messageContainer.attr('class', 'waa-message error').html(response.data.message);
                }
            },
            error: function() {
                messageContainer.attr('class', 'waa-message error').html('Si è verificato un errore durante l\'elaborazione della richiesta.');
            }
        });
    });
    */
    
    /**
     * Gestione form di utilizzo assicurazione
     */
    /*
    $('.waa-use-insurance-form').on('submit', function(e) {
        e.preventDefault();
        
        const form = $(this);
        const messageContainer = $('#waa-message');
        
        // Mostro messaggio di caricamento
        messageContainer.attr('class', 'waa-message').show().html('Elaborazione in corso...');
        
        // Recupero dati form
        const formData = new FormData(form[0]);
        formData.append('action', 'waa_use_insurance');
        formData.append('nonce', waa_params.nonce);
        
        // Invio richiesta AJAX
        $.ajax({
            url: waa_params.ajax_url,
            type: 'POST',
            data: formData,
            processData: false,
            contentType: false,
            success: function(response) {
                if (response.success) {
                    messageContainer.attr('class', 'waa-message success').html(response.data.message);
                    
                    // Ricarico la pagina se richiesto
                    if (response.data.reload) {
                        setTimeout(function() {
                            location.reload();
                        }, 1500);
                    }
                } else {
                    messageContainer.attr('class', 'waa-message error').html(response.data.message);
                }
            },
            error: function() {
                messageContainer.attr('class', 'waa-message error').html('Si è verificato un errore durante l\'elaborazione della richiesta.');
            }
        });
    });
    */
    
    /**
     * Funzioni di supporto
     */
    
    // Funzione per formattare la data in formato leggibile (opzionale)
    function formatDate(date) {
        const options = { year: 'numeric', month: '2-digit', day: '2-digit' };
        return new Date(date).toLocaleDateString('it-IT', options);
    }
    
    // Imposto la data di oggi come valore predefinito per i campi data
    $('input[type="date"]').each(function() {
        if (!$(this).val()) {
            const today = new Date().toISOString().split('T')[0];
            $(this).val(today);
        }
    });
}); 
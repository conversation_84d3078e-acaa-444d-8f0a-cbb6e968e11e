/*! elementor - v3.30.0 - 09-07-2025 */
"use strict";(self.webpackChunkelementorFrontend=self.webpackChunkelementorFrontend||[]).push([[457],{3905:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0,n(4846),n(6211);class Counter extends elementorModules.frontend.handlers.Base{getDefaultSettings(){return{selectors:{counterNumber:".elementor-counter-number"}}}getDefaultElements(){const e=this.getSettings("selectors");return{$counterNumber:this.$element.find(e.counterNumber)}}onInit(){super.onInit(),this.intersectionObserver=elementorModules.utils.Scroll.scrollObserver({callback:e=>{if(e.isInViewport){this.intersectionObserver.unobserve(this.elements.$counterNumber[0]);const e=this.elements.$counterNumber.data(),t=e.toValue.toString().match(/\.(.*)/);t&&(e.rounding=t[1].length),this.elements.$counterNumber.numerator(e)}}}),this.intersectionObserver.observe(this.elements.$counterNumber[0])}}t.default=Counter}}]);
(globalThis.webpackChunkwebpackWcBlocksMainJsonp=globalThis.webpackChunkwebpackWcBlocksMainJsonp||[]).push([[4442],{3848:(e,t,o)=>{"use strict";o.r(t),o.d(t,{Block:()=>u,default:()=>d});var s=o(7723),n=o(4921),r=o(4656),c=o(415),l=o(371),a=o(1616),i=(o(4313),o(790));const u=e=>{const{className:t,align:o,isDescendentOfSingleProductTemplate:a}=e,u=(0,l.p)(e),{parentClassName:d}=(0,c.useInnerBlockLayoutContext)(),{product:y}=(0,c.useProductDataContext)();if(!(y.id&&y.on_sale||a))return null;const f="string"==typeof o?`wc-block-components-product-sale-badge--align-${o}`:"";return(0,i.jsx)("div",{className:(0,n.A)("wc-block-components-product-sale-badge",t,f,{[`${d}__product-onsale`]:d},u.className),style:u.style,children:(0,i.jsx)(r.Label,{label:(0,s.__)("Sale","woocommerce"),screenReaderLabel:(0,s.__)("Product on sale","woocommerce")})})},d=(0,a.withProductDataContext)(u)},371:(e,t,o)=>{"use strict";o.d(t,{p:()=>l});var s=o(4921),n=o(3993),r=o(219),c=o(17);const l=e=>{const t=(e=>{const t=(0,n.isObject)(e)?e:{style:{}};let o=t.style;return(0,n.isString)(o)&&(o=JSON.parse(o)||{}),(0,n.isObject)(o)||(o={}),{...t,style:o}})(e),o=(0,c.BK)(t),l=(0,c.aR)(t),a=(0,c.fo)(t),i=(0,r.x)(t);return{className:(0,s.A)(i.className,o.className,l.className,a.className),style:{...i.style,...o.style,...l.style,...a.style}}}},219:(e,t,o)=>{"use strict";o.d(t,{x:()=>n});var s=o(3993);const n=e=>{const t=(0,s.isObject)(e.style.typography)?e.style.typography:{},o=(0,s.isString)(t.fontFamily)?t.fontFamily:"";return{className:e.fontFamily?`has-${e.fontFamily}-font-family`:o,style:{fontSize:e.fontSize?`var(--wp--preset--font-size--${e.fontSize})`:t.fontSize,fontStyle:t.fontStyle,fontWeight:t.fontWeight,letterSpacing:t.letterSpacing,lineHeight:t.lineHeight,textDecoration:t.textDecoration,textTransform:t.textTransform}}}},17:(e,t,o)=>{"use strict";o.d(t,{BK:()=>i,aR:()=>u,fo:()=>d});var s=o(4921),n=o(7356),r=o(9786),c=o(3993);function l(e={}){const t={};return(0,r.getCSSRules)(e,{selector:""}).forEach((e=>{t[e.key]=e.value})),t}function a(e,t){return e&&t?`has-${(0,n.c)(t)}-${e}`:""}function i(e){const{backgroundColor:t,textColor:o,gradient:n,style:r}=e,i=a("background-color",t),u=a("color",o),d=function(e){if(e)return`has-${e}-gradient-background`}(n),y=d||r?.color?.gradient;return{className:(0,s.A)(u,d,{[i]:!y&&!!i,"has-text-color":o||r?.color?.text,"has-background":t||r?.color?.background||n||r?.color?.gradient,"has-link-color":(0,c.isObject)(r?.elements?.link)?r?.elements?.link?.color:void 0}),style:l({color:r?.color||{}})}}function u(e){const t=e.style?.border||{};return{className:function(e){const{borderColor:t,style:o}=e,n=t?a("border-color",t):"";return(0,s.A)({"has-border-color":!!t||!!o?.border?.color,[n]:!!n})}(e),style:l({border:t})}}function d(e){return{className:void 0,style:l({spacing:e.style?.spacing||{}})}}},4313:()=>{}}]);
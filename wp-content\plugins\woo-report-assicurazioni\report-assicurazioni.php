<?php
/**
 * Plugin Name: Report Assicurazioni
 * Plugin URI: https://example.com/report-assicurazioni
 * Description: Plugin per la generazione di report dei prodotti Assicurazioni.
 * Version: 1.0.0
 * Author: <PERSON> '<PERSON><PERSON><PERSON>' <PERSON>
 * Author URI: https://github.com/JoJoD3v
 * Text Domain: report-assicurazioni
 * Domain Path: /languages
 * Requires at least: 5.0
 * Tested up to: 6.4
 * Requires PHP: 7.4
 * WC requires at least: 5.0
 * WC tested up to: 8.0
 * License: GPL v2 or later
 * License URI: https://www.gnu.org/licenses/gpl-2.0.html
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

// Define plugin constants
define('REPORT_ASSICURAZIONI_VERSION', '1.0.0');
define('REPORT_ASSICURAZIONI_PLUGIN_DIR', plugin_dir_path(__FILE__));
define('REPORT_ASSICURAZIONI_PLUGIN_URL', plugin_dir_url(__FILE__));
define('REPORT_ASSICURAZIONI_PLUGIN_BASENAME', plugin_basename(__FILE__));

/**
 * Main plugin class
 */
class Report_Assicurazioni {

    /**
     * Single instance of the class
     */
    private static $instance = null;

    /**
     * Get single instance
     */
    public static function get_instance() {
        if (null === self::$instance) {
            self::$instance = new self();
        }
        return self::$instance;
    }

    /**
     * Constructor
     */
    private function __construct() {
        add_action('plugins_loaded', array($this, 'init'));
        register_activation_hook(__FILE__, array($this, 'activate'));
        register_deactivation_hook(__FILE__, array($this, 'deactivate'));
    }

    /**
     * Initialize the plugin
     */
    public function init() {
        // Check if WooCommerce is active
        if (!class_exists('WooCommerce')) {
            add_action('admin_notices', array($this, 'woocommerce_missing_notice'));
            return;
        }

        // Load text domain
        add_action('init', array($this, 'load_textdomain'));

        // Include required files
        $this->includes();

        // Initialize admin functionality
        if (is_admin()) {
            new Report_Assicurazioni_Admin();
        }
    }

    /**
     * Load text domain for translations
     */
    public function load_textdomain() {
        $locale = apply_filters('plugin_locale', get_locale(), 'report-assicurazioni');
        $mo_file = REPORT_ASSICURAZIONI_PLUGIN_DIR . 'languages/report-assicurazioni-' . $locale . '.mo';

        if (file_exists($mo_file)) {
            load_textdomain('report-assicurazioni', $mo_file);
        } else {
            load_plugin_textdomain('report-assicurazioni', false, dirname(REPORT_ASSICURAZIONI_PLUGIN_BASENAME) . '/languages');
        }
    }

    /**
     * Include required files
     */
    private function includes() {
        require_once REPORT_ASSICURAZIONI_PLUGIN_DIR . 'includes/class-admin.php';
        require_once REPORT_ASSICURAZIONI_PLUGIN_DIR . 'includes/class-report-generator.php';
        require_once REPORT_ASSICURAZIONI_PLUGIN_DIR . 'includes/class-pdf-exporter.php';
    }

    /**
     * Plugin activation
     */
    public function activate() {
        // Check WooCommerce dependency
        if (!class_exists('WooCommerce')) {
            deactivate_plugins(REPORT_ASSICURAZIONI_PLUGIN_BASENAME);
            wp_die(
                __('Report Assicurazioni requires WooCommerce to be installed and active.', 'report-assicurazioni'),
                __('Plugin Activation Error', 'report-assicurazioni'),
                array('back_link' => true)
            );
        }

        // Create necessary database tables or options if needed
        $this->create_tables();
    }

    /**
     * Plugin deactivation
     */
    public function deactivate() {
        // Clean up if necessary
    }

    /**
     * Create necessary database tables
     */
    private function create_tables() {
        // Add any custom tables if needed in the future
        // For now, we'll use existing WooCommerce tables
    }

    /**
     * WooCommerce missing notice
     */
    public function woocommerce_missing_notice() {
        ?>
        <div class="notice notice-error">
            <p><?php _e('Report Assicurazioni requires WooCommerce to be installed and active.', 'report-assicurazioni'); ?></p>
        </div>
        <?php
    }
}

// Initialize the plugin
Report_Assicurazioni::get_instance();

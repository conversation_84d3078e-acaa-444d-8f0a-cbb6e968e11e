(globalThis.webpackChunkwebpackWcBlocksMainJsonp=globalThis.webpackChunkwebpackWcBlocksMainJsonp||[]).push([[2388],{1308:(e,r,c)=>{"use strict";c.r(r),c.d(r,{Block:()=>u,default:()=>p});var t=c(4921),s=c(6711),o=c(910),a=c(415),n=c(371),l=c(1616),i=c(790);const u=e=>{const{className:r,textAlign:c,isDescendentOfSingleProductTemplate:l}=e,u=(0,n.p)(e),{parentName:p,parentClassName:m}=(0,a.useInnerBlockLayoutContext)(),{product:d}=(0,a.useProductDataContext)(),y="woocommerce/all-products"===p,g=l&&!("woocommerce/add-to-cart-with-options-grouped-product-item"===p),x=(0,t.A)("wc-block-components-product-price",r,u.className,{[`${m}__product-price`]:m});if(!d.id&&!l){const e=(0,i.jsx)(s.A,{align:c,className:x});return y?(0,i.jsx)("div",{className:"wp-block-woocommerce-product-price",children:e}):e}const N=d.prices,_=g?(0,o.getCurrencyFromPriceResponse)():(0,o.getCurrencyFromPriceResponse)(N),f="5000",h=N.price!==N.regular_price,b=(0,t.A)({[`${m}__product-price__value`]:m,[`${m}__product-price__value--on-sale`]:h}),P=(0,i.jsx)(s.A,{align:c,className:x,style:u.style,regularPriceStyle:u.style,priceStyle:u.style,priceClassName:b,currency:_,price:g?f:N.price,minPrice:N?.price_range?.min_amount,maxPrice:N?.price_range?.max_amount,regularPrice:g?f:N.regular_price,regularPriceClassName:(0,t.A)({[`${m}__product-price__regular`]:m})});return y?(0,i.jsx)("div",{className:"wp-block-woocommerce-product-price",children:P}):P},p=e=>e.isDescendentOfSingleProductTemplate?(0,i.jsx)(u,{...e}):(0,l.withProductDataContext)(u)(e)},6711:(e,r,c)=>{"use strict";c.d(r,{A:()=>p});var t=c(7723),s=c(4656),o=c(4921),a=c(910),n=c(6087),l=(c(8501),c(790));const i=({currency:e,maxPrice:r,minPrice:c,priceClassName:n,priceStyle:i={}})=>(0,l.jsxs)(l.Fragment,{children:[(0,l.jsx)("span",{className:"screen-reader-text",children:(0,t.sprintf)(/* translators: %1$s min price, %2$s max price */ /* translators: %1$s min price, %2$s max price */
(0,t.__)("Price between %1$s and %2$s","woocommerce"),(0,a.formatPrice)(c),(0,a.formatPrice)(r))}),(0,l.jsxs)("span",{"aria-hidden":!0,children:[(0,l.jsx)(s.FormattedMonetaryAmount,{className:(0,o.A)("wc-block-components-product-price__value",n),currency:e,value:c,style:i})," — ",(0,l.jsx)(s.FormattedMonetaryAmount,{className:(0,o.A)("wc-block-components-product-price__value",n),currency:e,value:r,style:i})]})]}),u=({currency:e,regularPriceClassName:r,regularPriceStyle:c,regularPrice:a,priceClassName:n,priceStyle:i,price:u})=>(0,l.jsxs)(l.Fragment,{children:[(0,l.jsx)("span",{className:"screen-reader-text",children:(0,t.__)("Previous price:","woocommerce")}),(0,l.jsx)(s.FormattedMonetaryAmount,{currency:e,renderText:e=>(0,l.jsx)("del",{className:(0,o.A)("wc-block-components-product-price__regular",r),style:c,children:e}),value:a}),(0,l.jsx)("span",{className:"screen-reader-text",children:(0,t.__)("Discounted price:","woocommerce")}),(0,l.jsx)(s.FormattedMonetaryAmount,{currency:e,renderText:e=>(0,l.jsx)("ins",{className:(0,o.A)("wc-block-components-product-price__value","is-discounted",n),style:i,children:e}),value:u})]}),p=({align:e,className:r,currency:c,format:t="<price/>",maxPrice:a,minPrice:p,price:m,priceClassName:d,priceStyle:y,regularPrice:g,regularPriceClassName:x,regularPriceStyle:N,style:_})=>{const f=(0,o.A)(r,"price","wc-block-components-product-price",{[`wc-block-components-product-price--align-${e}`]:e});t.includes("<price/>")||(t="<price/>",console.error("Price formats need to include the `<price/>` tag."));const h=g&&m&&m<g;let b=(0,l.jsx)("span",{className:(0,o.A)("wc-block-components-product-price__value",d)});return h?b=(0,l.jsx)(u,{currency:c,price:m,priceClassName:d,priceStyle:y,regularPrice:g,regularPriceClassName:x,regularPriceStyle:N}):void 0!==p&&void 0!==a?b=(0,l.jsx)(i,{currency:c,maxPrice:a,minPrice:p,priceClassName:d,priceStyle:y}):m&&(b=(0,l.jsx)(s.FormattedMonetaryAmount,{className:(0,o.A)("wc-block-components-product-price__value",d),currency:c,value:m,style:y})),(0,l.jsx)("span",{className:f,style:_,children:(0,n.createInterpolateElement)(t,{price:b})})}},371:(e,r,c)=>{"use strict";c.d(r,{p:()=>n});var t=c(4921),s=c(3993),o=c(219),a=c(17);const n=e=>{const r=(e=>{const r=(0,s.isObject)(e)?e:{style:{}};let c=r.style;return(0,s.isString)(c)&&(c=JSON.parse(c)||{}),(0,s.isObject)(c)||(c={}),{...r,style:c}})(e),c=(0,a.BK)(r),n=(0,a.aR)(r),l=(0,a.fo)(r),i=(0,o.x)(r);return{className:(0,t.A)(i.className,c.className,n.className,l.className),style:{...i.style,...c.style,...n.style,...l.style}}}},219:(e,r,c)=>{"use strict";c.d(r,{x:()=>s});var t=c(3993);const s=e=>{const r=(0,t.isObject)(e.style.typography)?e.style.typography:{},c=(0,t.isString)(r.fontFamily)?r.fontFamily:"";return{className:e.fontFamily?`has-${e.fontFamily}-font-family`:c,style:{fontSize:e.fontSize?`var(--wp--preset--font-size--${e.fontSize})`:r.fontSize,fontStyle:r.fontStyle,fontWeight:r.fontWeight,letterSpacing:r.letterSpacing,lineHeight:r.lineHeight,textDecoration:r.textDecoration,textTransform:r.textTransform}}}},17:(e,r,c)=>{"use strict";c.d(r,{BK:()=>i,aR:()=>u,fo:()=>p});var t=c(4921),s=c(7356),o=c(9786),a=c(3993);function n(e={}){const r={};return(0,o.getCSSRules)(e,{selector:""}).forEach((e=>{r[e.key]=e.value})),r}function l(e,r){return e&&r?`has-${(0,s.c)(r)}-${e}`:""}function i(e){const{backgroundColor:r,textColor:c,gradient:s,style:o}=e,i=l("background-color",r),u=l("color",c),p=function(e){if(e)return`has-${e}-gradient-background`}(s),m=p||o?.color?.gradient;return{className:(0,t.A)(u,p,{[i]:!m&&!!i,"has-text-color":c||o?.color?.text,"has-background":r||o?.color?.background||s||o?.color?.gradient,"has-link-color":(0,a.isObject)(o?.elements?.link)?o?.elements?.link?.color:void 0}),style:n({color:o?.color||{}})}}function u(e){const r=e.style?.border||{};return{className:function(e){const{borderColor:r,style:c}=e,s=r?l("border-color",r):"";return(0,t.A)({"has-border-color":!!r||!!c?.border?.color,[s]:!!s})}(e),style:n({border:r})}}function p(e){return{className:void 0,style:n({spacing:e.style?.spacing||{}})}}},8501:()=>{}}]);
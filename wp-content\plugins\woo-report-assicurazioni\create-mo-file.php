<?php
/**
 * Script per creare il file .mo dalla traduzione italiana
 * Questo script crea un file .mo di base che WordPress può utilizzare
 */

// Traduzioni principali del plugin
$translations = array(
    'Report Assicurazioni requires WooCommerce to be installed and active.' => 'Report Assicurazioni richiede che WooCommerce sia installato e attivo.',
    'Plugin Activation Error' => 'Errore di Attivazione Plugin',
    'Report Assicurazioni' => 'Report Assicurazioni',
    'Exporting...' => 'Esportazione in corso...',
    'Error exporting report. Please try again.' => 'Errore durante l\'esportazione del report. Riprova.',
    'Loading report data...' => 'Caricamento dati report...',
    'Start date cannot be later than end date.' => 'La data di inizio non può essere successiva alla data di fine.',
    'Date cannot be in the future.' => 'La data non può essere nel futuro.',
    'Export completed successfully.' => 'Esportazione completata con successo.',
    'No data available for export.' => 'Nessun dato disponibile per l\'esportazione.',
    'Start Date' => 'Data Inizio',
    'End Date' => 'Data Fine',
    'Search' => 'Cerca',
    'Search orders...' => 'Cerca ordini...',
    'Filter Report' => 'Filtra Report',
    'Export to PDF' => 'Esporta in PDF',
    'Order Number' => 'Numero Ordine',
    'Order Date' => 'Data Ordine',
    'Products' => 'Prodotti',
    'Total Amount' => 'Importo Totale',
    'No orders found matching the criteria.' => 'Nessun ordine trovato che corrisponda ai criteri.',
    'You do not have sufficient permissions to access this page.' => 'Non hai i permessi sufficienti per accedere a questa pagina.',
    'PDF export is not available. Please install a PDF library.' => 'L\'esportazione PDF non è disponibile. Installa una libreria PDF.',
    'Insurance Report' => 'Report Assicurazioni',
    'From: ' => 'Dal: ',
    'To: ' => 'Al: ',
    'Summary' => 'Riepilogo',
    'Total Orders: ' => 'Totale Ordini: ',
    'Total Amount: ' => 'Importo Totale: ',
    'Average Order Value: ' => 'Valore Medio Ordine: ',
    'Order #' => 'Ordine N°',
    'Date' => 'Data',
    'Total' => 'Totale',
    'Generated on: ' => 'Generato il: ',
);

// Funzione per creare il file .mo
function create_mo_file($translations, $filename) {
    $entries = count($translations);
    
    // Header del file MO (magic number, version, entries count, etc.)
    $mo_data = pack('V', 0x950412de); // Magic number
    $mo_data .= pack('V', 0);          // Version
    $mo_data .= pack('V', $entries);   // Number of entries
    $mo_data .= pack('V', 28);         // Offset of key table
    $mo_data .= pack('V', 28 + ($entries * 8)); // Offset of value table
    $mo_data .= pack('V', 0);          // Hash table size
    $mo_data .= pack('V', 0);          // Hash table offset
    
    $keys = array_keys($translations);
    $values = array_values($translations);
    
    // Calcola gli offset per le stringhe
    $key_offsets = array();
    $value_offsets = array();
    $key_data = '';
    $value_data = '';
    
    foreach ($keys as $key) {
        $key_offsets[] = array(strlen($key), strlen($key_data));
        $key_data .= $key . "\0";
    }
    
    foreach ($values as $value) {
        $value_offsets[] = array(strlen($value), strlen($value_data));
        $value_data .= $value . "\0";
    }
    
    // Offset base per i dati delle stringhe
    $key_data_offset = 28 + ($entries * 16);
    $value_data_offset = $key_data_offset + strlen($key_data);
    
    // Tabella degli offset delle chiavi
    foreach ($key_offsets as $offset) {
        $mo_data .= pack('V', $offset[0]); // Lunghezza
        $mo_data .= pack('V', $key_data_offset + $offset[1]); // Offset
    }
    
    // Tabella degli offset dei valori
    foreach ($value_offsets as $offset) {
        $mo_data .= pack('V', $offset[0]); // Lunghezza
        $mo_data .= pack('V', $value_data_offset + $offset[1]); // Offset
    }
    
    // Dati delle stringhe
    $mo_data .= $key_data;
    $mo_data .= $value_data;
    
    // Scrivi il file
    file_put_contents($filename, $mo_data);
    
    return true;
}

// Crea il file .mo
$mo_file = __DIR__ . '/languages/report-assicurazioni-it_IT.mo';

if (create_mo_file($translations, $mo_file)) {
    echo "File .mo creato con successo: $mo_file\n";
    echo "Traduzioni incluse: " . count($translations) . "\n";
    echo "Dimensione file: " . filesize($mo_file) . " bytes\n";

    // Crea anche un file di backup del .po aggiornato
    $po_backup = __DIR__ . '/languages/report-assicurazioni-it_IT-backup.po';
    if (file_exists(__DIR__ . '/languages/report-assicurazioni-it_IT.po')) {
        copy(__DIR__ . '/languages/report-assicurazioni-it_IT.po', $po_backup);
        echo "Backup del file .po creato: $po_backup\n";
    }
} else {
    echo "Errore nella creazione del file .mo\n";
}

// Esegui automaticamente la creazione se chiamato direttamente
if (php_sapi_name() === 'cli' || !empty($_GET['create'])) {
    // Script eseguito
}
?>

(()=>{"use strict";var e,t,r,o={5580:(e,t,r)=>{const o=window.wp.blocks;var n=r(6052);const i=JSON.parse('{"$schema":"https://schemas.wp.org/trunk/block.json","apiVersion":3,"name":"woocommerce/product-review-rating","title":"Review Rating","category":"woocommerce","ancestor":["woocommerce/product-reviews"],"description":"Displays the rating of a product review.","textdomain":"woocommerce","usesContext":["commentId"],"attributes":{"textAlign":{"type":"string"}},"supports":{"interactivity":{"clientNavigation":true},"color":{"gradients":true,"__experimentalDefaultControls":{"background":true,"text":true}}}}');var a=r(4921),s=r(7723);const c=window.wp.blockEditor,l=window.wp.data,u=window.wc.data;var p=r(790);(0,o.registerBlockType)(i,{edit:function({context:{commentId:e},attributes:t,setAttributes:r}){const{textAlign:o}=t,n=(0,a.A)("wc-block-product-review-rating",{[`has-text-align-${o}`]:o}),i=(0,c.useBlockProps)({className:n}),d=(0,l.useSelect)((t=>{var r;const{getReview:o}=t(u.REVIEWS_STORE_NAME),n=e?o(Number(e)):null;return null!==(r=n?.rating)&&void 0!==r?r:4}),[e]),w={width:d/5*100+"%"},f=(0,s.sprintf)(/* translators: %f is referring to the average rating value */ /* translators: %f is referring to the average rating value */
(0,s.__)("Rated %f out of 5","woocommerce"),d),g={__html:(0,s.sprintf)(/* translators: %s is the rating value wrapped in HTML strong tags. */ /* translators: %s is the rating value wrapped in HTML strong tags. */
(0,s.__)("Rated %s out of 5","woocommerce"),(0,s.sprintf)('<strong class="rating">%f</strong>',d))};return(0,p.jsxs)(p.Fragment,{children:[(0,p.jsx)(c.BlockControls,{children:(0,p.jsx)(c.AlignmentToolbar,{value:t.textAlign,onChange:e=>{r({textAlign:e||""})}})}),(0,p.jsx)("div",{...i,children:(0,p.jsx)("div",{className:"wc-block-product-review-rating__stars",role:"img","aria-label":f,children:(0,p.jsx)("span",{style:w,dangerouslySetInnerHTML:g})})})]})},icon:n.A})},1609:e=>{e.exports=window.React},790:e=>{e.exports=window.ReactJSXRuntime},7723:e=>{e.exports=window.wp.i18n},5573:e=>{e.exports=window.wp.primitives}},n={};function i(e){var t=n[e];if(void 0!==t)return t.exports;var r=n[e]={exports:{}};return o[e].call(r.exports,r,r.exports,i),r.exports}i.m=o,e=[],i.O=(t,r,o,n)=>{if(!r){var a=1/0;for(u=0;u<e.length;u++){for(var[r,o,n]=e[u],s=!0,c=0;c<r.length;c++)(!1&n||a>=n)&&Object.keys(i.O).every((e=>i.O[e](r[c])))?r.splice(c--,1):(s=!1,n<a&&(a=n));if(s){e.splice(u--,1);var l=o();void 0!==l&&(t=l)}}return t}n=n||0;for(var u=e.length;u>0&&e[u-1][2]>n;u--)e[u]=e[u-1];e[u]=[r,o,n]},i.n=e=>{var t=e&&e.__esModule?()=>e.default:()=>e;return i.d(t,{a:t}),t},r=Object.getPrototypeOf?e=>Object.getPrototypeOf(e):e=>e.__proto__,i.t=function(e,o){if(1&o&&(e=this(e)),8&o)return e;if("object"==typeof e&&e){if(4&o&&e.__esModule)return e;if(16&o&&"function"==typeof e.then)return e}var n=Object.create(null);i.r(n);var a={};t=t||[null,r({}),r([]),r(r)];for(var s=2&o&&e;"object"==typeof s&&!~t.indexOf(s);s=r(s))Object.getOwnPropertyNames(s).forEach((t=>a[t]=()=>e[t]));return a.default=()=>e,i.d(n,a),n},i.d=(e,t)=>{for(var r in t)i.o(t,r)&&!i.o(e,r)&&Object.defineProperty(e,r,{enumerable:!0,get:t[r]})},i.o=(e,t)=>Object.prototype.hasOwnProperty.call(e,t),i.r=e=>{"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})},i.j=8833,(()=>{var e={8833:0};i.O.j=t=>0===e[t];var t=(t,r)=>{var o,n,[a,s,c]=r,l=0;if(a.some((t=>0!==e[t]))){for(o in s)i.o(s,o)&&(i.m[o]=s[o]);if(c)var u=c(i)}for(t&&t(r);l<a.length;l++)n=a[l],i.o(e,n)&&e[n]&&e[n][0](),e[n]=0;return i.O(u)},r=globalThis.webpackChunkwebpackWcBlocksMainJsonp=globalThis.webpackChunkwebpackWcBlocksMainJsonp||[];r.forEach(t.bind(null,0)),r.push=t.bind(null,r.push.bind(r))})();var a=i.O(void 0,[94],(()=>i(5580)));a=i.O(a),((this.wc=this.wc||{}).blocks=this.wc.blocks||{})["product-review-rating"]=a})();
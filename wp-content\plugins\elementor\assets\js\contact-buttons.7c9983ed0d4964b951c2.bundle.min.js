/*! elementor - v3.30.0 - 09-07-2025 */
"use strict";(self.webpackChunkelementorFrontend=self.webpackChunkelementorFrontend||[]).push([[1],{6285:(t,e,n)=>{var s=n(6784);Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var i=s(n(7224)),a=s(n(442));class ContactButtonsHandler extends i.default{getDefaultSettings(){return{selectors:{main:".e-contact-buttons",content:".e-contact-buttons__content",contentWrapper:".e-contact-buttons__content-wrapper",chatButton:".e-contact-buttons__chat-button",closeButton:".e-contact-buttons__close-button",messageBubbleTime:".e-contact-buttons__message-bubble-time"},constants:{entranceAnimation:"style_chat_box_entrance_animation",exitAnimation:"style_chat_box_exit_animation",chatButtonAnimation:"style_chat_button_animation",animated:"animated",animatedWrapper:"animated-wrapper",visible:"visible",reverse:"reverse",hidden:"hidden",hasAnimations:"has-animations",hasEntranceAnimation:"has-entrance-animation",none:"none"}}}getDefaultElements(){const t=this.getSettings("selectors");return{main:this.$element[0].querySelector(t.main),content:this.$element[0].querySelector(t.content),contentWrapper:this.$element[0].querySelector(t.contentWrapper),chatButton:this.$element[0].querySelector(t.chatButton),closeButton:this.$element[0].querySelector(t.closeButton),messageBubbleTime:this.$element[0].querySelector(t.messageBubbleTime)}}getResponsiveSetting(t){const e=elementorFrontend.getCurrentDeviceMode();return elementorFrontend.utils.controls.getResponsiveControlValue(this.getElementSettings(),t,"",e)}bindEvents(){this.elements.closeButton&&this.elements.closeButton.addEventListener("click",this.closeChatBox.bind(this)),this.elements.chatButton&&(this.elements.chatButton.addEventListener("click",this.onChatButtonClick.bind(this)),this.elements.chatButton.addEventListener("animationend",this.removeChatButtonAnimationClasses.bind(this))),this.elements.content&&this.elements.content.addEventListener("animationend",this.removeAnimationClasses.bind(this)),this.elements.contentWrapper&&window.addEventListener("keyup",this.onDocumentKeyup.bind(this))}contentWrapperIsHidden(t){if(!this.elements.contentWrapper)return!1;const{hidden:e}=this.getSettings("constants");return!0===t?(this.elements.contentWrapper.classList.add(e),void this.elements.contentWrapper.setAttribute("aria-hidden","true")):!1===t?(this.elements.contentWrapper.classList.remove(e),void this.elements.contentWrapper.setAttribute("aria-hidden","false")):this.elements.contentWrapper.classList.contains(e)}onDocumentKeyup(t){27===t.keyCode&&this.elements.main&&!this.contentWrapperIsHidden()&&this.elements.main.contains(document.activeElement)&&this.closeChatBox()}removeAnimationClasses(){if(!this.elements.content)return;const{reverse:t,entranceAnimation:e,exitAnimation:n,animated:s,visible:i}=this.getSettings("constants"),a=this.elements.content.classList.contains(t),o=this.getResponsiveSetting(e),c=this.getResponsiveSetting(n);a?(this.elements.content.classList.remove(s),this.elements.content.classList.remove(t),c&&this.elements.content.classList.remove(c),this.elements.content.classList.remove(i)):(this.elements.content.classList.remove(s),o&&this.elements.content.classList.remove(o),this.elements.content.classList.add(i))}chatBoxEntranceAnimation(){const{entranceAnimation:t,animated:e,animatedWrapper:n,none:s}=this.getSettings("constants"),i=this.getResponsiveSetting(t);i&&s!==i&&(this.elements.content&&(this.elements.content.classList.add(e),this.elements.content.classList.add(i)),this.elements.contentWrapper&&this.elements.contentWrapper.classList.remove(n))}chatBoxExitAnimation(){const{reverse:t,exitAnimation:e,animated:n,animatedWrapper:s,none:i}=this.getSettings("constants"),a=this.getResponsiveSetting(e);a&&i!==a&&(this.elements.content&&(this.elements.content.classList.add(n),this.elements.content.classList.add(t),this.elements.content.classList.add(a)),this.elements.contentWrapper&&this.elements.contentWrapper.classList.add(s))}openChatBox(){const{hasAnimations:t,visible:e}=this.getSettings("constants");this.elements.main&&this.elements.main.classList.contains(t)?this.chatBoxEntranceAnimation():this.elements.content&&this.elements.content.classList.add(e),this.elements.contentWrapper&&(this.contentWrapperIsHidden(!1),elementorFrontend.isEditMode()||(this.elements.contentWrapper.setAttribute("tabindex","0"),this.elements.contentWrapper.focus({focusVisible:!0}))),this.elements.chatButton&&this.elements.chatButton.setAttribute("aria-expanded","true"),this.elements.closeButton&&this.elements.closeButton.setAttribute("aria-expanded","true")}closeChatBox(){const{hasAnimations:t,visible:e}=this.getSettings("constants");this.elements.main&&this.elements.main.classList.contains(t)?this.chatBoxExitAnimation():this.elements.content&&this.elements.content.classList.remove(e),this.elements.contentWrapper&&this.contentWrapperIsHidden(!0),this.elements.chatButton&&(this.elements.chatButton.setAttribute("aria-expanded","false"),this.elements.chatButton.focus({focusVisible:!0})),this.elements.closeButton&&this.elements.closeButton.setAttribute("aria-expanded","false")}onChatButtonClick(){this.elements.contentWrapper&&this.contentWrapperIsHidden()?this.openChatBox():this.closeChatBox()}initMessageBubbleTime(){if(!this.elements.messageBubbleTime)return;const t="12h"===this.elements.messageBubbleTime.dataset.timeFormat;this.elements.messageBubbleTime.innerHTML=new Intl.DateTimeFormat("default",{hour12:t,hour:"numeric",minute:"numeric"}).format(new Date)}removeChatButtonAnimationClasses(){if(!this.elements.chatButton)return;const{chatButtonAnimation:t,visible:e}=this.getSettings("constants");this.elements.chatButton.classList.remove(t),this.elements.chatButton.classList.add(e)}initChatButtonEntranceAnimation(){const{none:t,chatButtonAnimation:e}=this.getSettings("constants"),n=this.getResponsiveSetting(e);n&&t!==n&&this.elements.chatButton.classList.add(n)}initDefaultState(){if(this.elements.contentWrapper){const t=this.contentWrapperIsHidden();this.elements.chatButton&&this.elements.chatButton.setAttribute("aria-expanded",!t),this.elements.closeButton&&this.elements.closeButton.setAttribute("aria-expanded",!t)}elementorFrontend.isEditMode()&&"floating-buttons"===elementor?.config?.document?.type&&this.openChatBox()}setupInnerContainer(){this.elements.main.closest(".e-con-inner").classList.add("e-con-inner--floating-buttons")}onInit(){const{hasEntranceAnimation:t}=this.getSettings("constants");super.onInit(...arguments),this.clickTrackingHandler=new a.default({$element:this.$element}),this.elements.messageBubbleTime&&this.initMessageBubbleTime(),this.initDefaultState(),this.elements.chatButton&&this.elements.chatButton.classList.contains(t)&&this.initChatButtonEntranceAnimation(),this.setupInnerContainer()}}e.default=ContactButtonsHandler},442:(t,e,n)=>{var s=n(6784);Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,n(5724),n(4846),n(9655);var i=s(n(7224));class ClickTrackingHandler extends i.default{clicks=[];getDefaultSettings(){return{selectors:{contentWrapper:".e-contact-buttons__content-wrapper",contactButtonCore:".e-contact-buttons__send-button",contentWrapperFloatingBars:".e-floating-bars",floatingBarCTAButton:".e-floating-bars__cta-button",elementorWrapper:'[data-elementor-type="floating-buttons"]'}}}getDefaultElements(){const t=this.getSettings("selectors");return{contentWrapper:this.$element[0].querySelector(t.contentWrapper),contentWrapperFloatingBars:this.$element[0].querySelector(t.contentWrapperFloatingBars)}}bindEvents(){this.elements.contentWrapper&&this.elements.contentWrapper.addEventListener("click",this.onChatButtonTrackClick.bind(this)),this.elements.contentWrapperFloatingBars&&this.elements.contentWrapperFloatingBars.addEventListener("click",this.onChatButtonTrackClick.bind(this)),window.addEventListener("beforeunload",(()=>{this.clicks.length>0&&this.sendClicks()}))}onChatButtonTrackClick(t){const e=t.target||t.srcElement,n=this.getSettings("selectors");(e.matches(n.contactButtonCore)||e.closest(n.contactButtonCore)||e.matches(n.floatingBarCTAButton)||e.closest(n.floatingBarCTAButton))&&this.getDocumentIdAndTrack(e,n)}getDocumentIdAndTrack(t,e){const n=t.closest(e.elementorWrapper).dataset.elementorId;this.trackClick(n)}trackClick(t){t&&(this.clicks.push(t),this.clicks.length>=10&&this.sendClicks())}sendClicks(){const t=new FormData;t.append("action","elementor_send_clicks"),t.append("_nonce",elementorFrontendConfig?.nonces?.floatingButtonsClickTracking),this.clicks.forEach((e=>t.append("clicks[]",e))),fetch(elementorFrontendConfig?.urls?.ajaxurl,{method:"POST",body:t}).then((()=>{this.clicks=[]}))}}e.default=ClickTrackingHandler}}]);
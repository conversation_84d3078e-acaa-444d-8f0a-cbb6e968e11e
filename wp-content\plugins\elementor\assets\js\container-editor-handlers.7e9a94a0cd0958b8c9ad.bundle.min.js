/*! elementor - v3.30.0 - 09-07-2025 */
"use strict";(self.webpackChunkelementorFrontend=self.webpackChunkelementorFrontend||[]).push([[768],{3323:(e,t,i)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0,i(6281),i(5724),i(4846),i(6211),i(9655);class GridContainer extends elementorModules.frontend.handlers.Base{__construct(e){super.__construct(e),this.onDeviceModeChange=this.onDeviceModeChange.bind(this),this.updateEmptyViewHeight=this.updateEmptyViewHeight.bind(this)}isActive(){return elementorFrontend.isEditMode()}getDefaultSettings(){return{selectors:{gridOutline:".e-grid-outline",directGridOverlay:":scope > .e-grid-outline",boxedContainer:":scope > .e-con-inner",emptyView:".elementor-empty-view"},classes:{outline:"e-grid-outline",outlineItem:"e-grid-outline-item",gridItemControls:["_heading_grid_item","_grid_column","_grid_column_custom","_grid_row","_grid_row_custom","heading_grid_item","grid_column","grid_column_custom","grid_row","grid_row_custom"].map((e=>`[class*="elementor-control-${e}"]`)).join(", ")}}}getDefaultElements(){const e=this.getSettings("selectors");return{outlineParentContainer:null,gridOutline:this.findElement(e.gridOutline),directChildGridOverlay:this.findElement(e.directGridOverlay),emptyView:this.findElement(e.emptyView)[0],container:this.$element[0]}}onInit(){super.onInit(),this.initLayoutOverlay(),this.updateEmptyViewHeight(),elementor.hooks.addAction("panel/open_editor/container",this.onPanelShow)}handleGridControls(e,t){["_section_style","section_layout"].includes(e)&&(this.isItemInGridCell(t)||this.hideGridControls(t))}isItemInGridCell(e){const t=e?.getOption("editedElementView")?.getContainer();return"function"==typeof t?.parent?.model?.getSetting&&"grid"===t?.parent?.model?.getSetting("container_type")}hideGridControls(e){const t=this.getSettings("classes"),i=e?.el.querySelectorAll(t.gridItemControls);i.forEach((e=>{e.style.display="none"}))}onPanelShow(e,t){const i=t.get("settings").get("container_type"),n=e.$el.find("#elementor-panel__editor__help__link"),s="grid"===i?"https://go.elementor.com/widget-container-grid":"https://go.elementor.com/widget-container";n&&n.attr("href",s)}bindEvents(){elementorFrontend.elements.$window.on("resize",this.onDeviceModeChange),elementorFrontend.elements.$window.on("resize",this.updateEmptyViewHeight),this.addChildLifeCycleEventListeners(),elementor.channels.editor.on("section:activated",this.handleGridControls.bind(this))}unbindEvents(){this.removeChildLifeCycleEventListeners(),elementorFrontend.elements.$window.off("resize",this.onDeviceModeChange),elementorFrontend.elements.$window.off("resize",this.updateEmptyViewHeight),elementor.channels.editor.off("section:activated",this.handleGridControls.bind(this))}initLayoutOverlay(){this.getCorrectContainer();const e=this.getSettings("selectors"),t="grid"===this.getElementSettings("container_type");this.elements.emptyView=this.findElement(e.emptyView)[0],t&&this.elements?.emptyView&&(this.elements.emptyView.style.display=this.shouldRemoveEmptyView()?"none":"block"),this.shouldDrawOutline()&&(this.removeExistingOverlay(),this.createOverlayContainer(),this.createOverlayItems())}shouldDrawOutline(){const{grid_outline:e}=this.getElementSettings();return e}getCorrectContainer(){const e=this.elements.container,t=this.getDefaultSettings(),{selectors:{boxedContainer:i}}=t;this.elements.outlineParentContainer=e.querySelector(i)||e}removeExistingOverlay(){this.elements.gridOutline?.remove()}createOverlayContainer(){const{outlineParentContainer:e}=this.elements,{classes:{outline:t}}=this.getDefaultSettings(),i=document.createElement("div");i.classList.add(t),e.appendChild(i),this.elements.gridOutline=i,this.setGridOutlineDimensions()}createOverlayItems(){const{gridOutline:e}=this.elements,{classes:{outlineItem:t}}=this.getDefaultSettings(),i=this.getMaxOutlineElementsNumber();for(let n=0;n<i;n++){const i=document.createElement("div");i.classList.add(t),e.appendChild(i)}}getDeviceGridDimensions(){const e=elementor.channels.deviceMode.request("currentMode");return{rows:this.getControlValues("grid_rows_grid",e,"grid-template-rows")||1,columns:this.getControlValues("grid_columns_grid",e,"grid-template-columns")||1}}setGridOutlineDimensions(){const{gridOutline:e}=this.elements,{rows:t,columns:i}=this.getDeviceGridDimensions();e.style.gridTemplateColumns=i.value,e.style.gridTemplateRows=t.value}getControlValues(e,t,i){const n=this.getElementSettings(),{unit:s,size:o}=n[e],{outlineParentContainer:r}=this.elements,l=elementorFrontend.utils.controls.getResponsiveControlValue(n,e,"size",t),d=this.getComputedStyle(r,i),a=d.split(" ").length;let h;return h="custom"===s&&"string"==typeof l||o<a?{value:d}:{value:`repeat(${a}, 1fr)`},h={...h,length:a},h}getComputedStyle(e,t){return window?.getComputedStyle(e,null).getPropertyValue(t)}onElementChange(e){this.isControlThatMayAffectEmptyViewHeight(e)&&this.updateEmptyViewHeight();let t=["grid_rows_grid","grid_columns_grid","grid_gaps","container_type","boxed_width","content_width","width","height","min_height","padding","grid_auto_flow"];t=this.getResponsiveControlNames(t),t.includes(e)&&this.initLayoutOverlay()}isControlThatMayAffectEmptyViewHeight(e){return 0===e.indexOf("grid_rows_grid")||0===e.indexOf("grid_columns_grid")||0===e.indexOf("grid_auto_flow")}getResponsiveControlNames(e){const t=elementorFrontend.breakpoints.getActiveBreakpointsList(),i=[];for(const n of e)for(const e of t)i.push(`${n}_${e}`);return i.push(...e),i}onDeviceModeChange(){this.initLayoutOverlay()}addChildLifeCycleEventListeners(){this.lifecycleChangeListener=this.initLayoutOverlay.bind(this),window.addEventListener("elementor/editor/element-rendered",this.lifecycleChangeListener),window.addEventListener("elementor/editor/element-destroyed",this.lifecycleChangeListener)}removeChildLifeCycleEventListeners(){window.removeEventListener("elementor/editor/element-rendered",this.lifecycleChangeListener),window.removeEventListener("elementor/editor/element-destroyed",this.lifecycleChangeListener)}updateEmptyViewHeight(){if(this.shouldUpdateEmptyViewHeight()){const{emptyView:e}=this.elements,t=elementor.channels.deviceMode.request("currentMode"),i=this.getElementSettings(),n="desktop"===t?i.grid_rows_grid:i.grid_rows_grid+"_"+t;e?.style.removeProperty("min-height"),this.hasCustomUnit(n)&&this.isNotOnlyANumber(n)&&this.sizeNotEmpty(n)&&(e.style.minHeight="auto"),e?.offsetHeight<=0&&(e.style.minHeight="100px")}}shouldUpdateEmptyViewHeight(){return!!this.elements.container.querySelector(".elementor-empty-view")}hasCustomUnit(e){return"custom"===e?.unit}sizeNotEmpty(e){return""!==e?.size?.trim()}isNotOnlyANumber(e){return!/^\d+$/.test(e?.size)}shouldRemoveEmptyView(){const e=this.elements.outlineParentContainer.querySelectorAll(":scope > .elementor-element").length;if(0===e)return!1;return this.getMaxElementsNumber()<=e&&this.isFullFilled(e)}isFullFilled(e){const t=this.getDeviceGridDimensions(),{grid_auto_flow:i}=this.getElementSettings();return 0==e%t["row"===i?"columns":"rows"].length}getMaxOutlineElementsNumber(){const e=this.elements.outlineParentContainer.querySelectorAll(":scope > .elementor-element").length,t=this.getDeviceGridDimensions(),i=this.getMaxElementsNumber(),{grid_auto_flow:n}=this.getElementSettings(),s="row"===n?"columns":"rows",o=Math.ceil(e/t[s].length)*t[s].length;return i>o?i:o}getMaxElementsNumber(){const e=this.getElementSettings(),t=elementor.channels.deviceMode.request("currentMode"),{grid_auto_flow:i}=this.getElementSettings(),n=this.getDeviceGridDimensions();if("row"===i){const i=elementorFrontend.utils.controls.getResponsiveControlValue(e,"grid_rows_grid","size",t),s=isNaN(i)?i.split(" ").length:i;return n.columns.length*s}const s=elementorFrontend.utils.controls.getResponsiveControlValue(e,"grid_columns_grid","size",t),o=isNaN(s)?rows.split(" ").length:s;return n.rows.length*o}}t.default=GridContainer},8847:(e,t,i)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0,i(4846),i(6211);class Shapes extends elementorModules.frontend.handlers.Base{getDefaultSettings(){return{selectors:{container:"boxed"===this.getElementSettings("content_width")?"> .e-con-inner > .elementor-shape-%s":"> .elementor-shape-%s"},svgURL:elementorFrontend.config.urls.assets+"shapes/"}}getDefaultElements(){const e={},t=this.getSettings("selectors");return e.$topContainer=this.$element.find(t.container.replace("%s","top")),e.$bottomContainer=this.$element.find(t.container.replace("%s","bottom")),e}isActive(){return elementorFrontend.isEditMode()}getSvgURL(e,t){let i=this.getSettings("svgURL")+t+".svg";return elementor.config.additional_shapes&&e in elementor.config.additional_shapes&&(i=elementor.config.additional_shapes[e],-1<t.indexOf("-negative")&&(i=i.replace(".svg","-negative.svg"))),i}buildSVG(e){const t="shape_divider_"+e,i=this.getElementSettings(t),n=this.elements["$"+e+"Container"];if(n.attr("data-shape",i),!i)return void n.empty();let s=i;this.getElementSettings(t+"_negative")&&(s+="-negative");const o=this.getSvgURL(i,s);jQuery.get(o,(e=>{n.empty().append(e.childNodes[0])})),this.setNegative(e)}setNegative(e){this.elements["$"+e+"Container"].attr("data-negative",!!this.getElementSettings("shape_divider_"+e+"_negative"))}onInit(){this.isActive(this.getSettings())&&(super.onInit(...arguments),["top","bottom"].forEach((e=>{this.getElementSettings("shape_divider_"+e)&&this.buildSVG(e)})))}onElementChange(e){const t=e.match(/^shape_divider_(top|bottom)$/);if(t)return void this.buildSVG(t[1]);const i=e.match(/^shape_divider_(top|bottom)_negative$/);i&&(this.buildSVG(i[1]),this.setNegative(i[1]))}}t.default=Shapes}}]);
<?php
/**
 * Classe per la gestione delle email di notifica per le assicurazioni
 *
 * @package Woo_Attiva_Assicurazione
 */

// Prevengo l'accesso diretto al file
if (!defined('ABSPATH')) {
    exit;
}

/**
 * Classe per la gestione delle email di notifica per le assicurazioni
 */
class WAA_Email {

    /**
     * Inizializza la classe
     */
    public static function init() {
        // Filtri per le email
        add_filter('woocommerce_email_styles', array(__CLASS__, 'add_email_styles'));
    }

    /**
     * Aggiunge stili CSS alle email
     *
     * @param string $css CSS esistente
     * @return string CSS con gli stili aggiunti
     */
    public static function add_email_styles($css) {
        // Aggiungo stili per le email di assicurazione
        $css .= '
            .waa-insurance-table {
                width: 100%;
                border-collapse: collapse;
                margin-bottom: 20px;
            }
            .waa-insurance-table th, .waa-insurance-table td {
                padding: 10px;
                border: 1px solid #e5e5e5;
                text-align: left;
            }
            .waa-insurance-table th {
                background-color: #f8f8f8;
            }
            .waa-insurance-info {
                margin-bottom: 20px;
                padding: 15px;
                background-color: #f8f8f8;
                border: 1px solid #e5e5e5;
            }
            .waa-insurance-heading {
                font-size: 18px;
                margin-bottom: 15px;
                color: #7f54b3;
                border-bottom: 1px solid #e5e5e5;
                padding-bottom: 10px;
            }
        ';
        
        return $css;
    }

    /**
     * Invia l'email di registrazione dell'assicurazione
     *
     * @param WC_Order $order Oggetto ordine
     * @param array $insurance_data Dati dell'assicurazione registrata
     * @return bool Esito dell'invio
     */
    public static function send_registration_email($order, $insurance_data) {
        if (!$order || !is_a($order, 'WC_Order') || empty($insurance_data['customer_email'])) {
            error_log('WAA_Email: Impossibile inviare email - dati mancanti o non validi');
            return false;
        }

        $to = $insurance_data['customer_email'];
        $subject = sprintf(
            __('[%s] Registrazione Assicurazione - Ordine #%s', 'woo-attiva-assicurazione'),
            get_bloginfo('name'),
            $order->get_order_number()
        );
        
        ob_start();
        
        // Includo l'header dell'email
        $header_template = locate_template('woocommerce/emails/email-header.php');
        if (!empty($header_template)) {
            include $header_template;
        } else {
            wc_get_template('emails/email-header.php', array('email_heading' => $subject));
        }
        
        // Contenuto dell'email
        ?>
        <p><?php printf(
            __('Gentile %s,', 'woo-attiva-assicurazione'),
            esc_html($insurance_data['customer_name'])
        ); ?></p>
        
        <p><?php _e('La tua assicurazione è stata registrata con successo. Di seguito trovi i dettagli:', 'woo-attiva-assicurazione'); ?></p>
        
        <div class="waa-insurance-info">
            <h2 class="waa-insurance-heading"><?php _e('Dettagli Assicurazione', 'woo-attiva-assicurazione'); ?></h2>
            
            <p><strong><?php _e('Ordine:', 'woo-attiva-assicurazione'); ?></strong> #<?php echo esc_html($order->get_order_number()); ?></p>
            <p><strong><?php _e('Prodotto:', 'woo-attiva-assicurazione'); ?></strong> <?php echo esc_html($insurance_data['product_name']); ?></p>
            <p><strong><?php _e('IMEI:', 'woo-attiva-assicurazione'); ?></strong> <?php echo esc_html($insurance_data['imei']); ?></p>
            <p><strong><?php _e('Data di Attivazione:', 'woo-attiva-assicurazione'); ?></strong> <?php echo esc_html(date_i18n(get_option('date_format'), strtotime($insurance_data['activation_date']))); ?></p>
            <p><strong><?php _e('Stato:', 'woo-attiva-assicurazione'); ?></strong> <?php _e('Registrata', 'woo-attiva-assicurazione'); ?></p>
        </div>
        
        <p><?php _e('Conserva questa email come ricevuta della tua assicurazione.', 'woo-attiva-assicurazione'); ?></p>
        
        <p><?php _e('In caso di domande o necessità di supporto, non esitare a contattarci.', 'woo-attiva-assicurazione'); ?></p>
        
        <?php
        
        // Riepilogo dell'ordine (opzionale)
        do_action('woocommerce_email_order_details', $order, false, false, $to);
        
        // Include il footer dell'email
        $footer_template = locate_template('woocommerce/emails/email-footer.php');
        if (!empty($footer_template)) {
            include $footer_template;
        } else {
            wc_get_template('emails/email-footer.php');
        }
        
        $message = ob_get_clean();
        
        // Imposto gli headers per l'email HTML
        $headers = array(
            'Content-Type: text/html; charset=UTF-8',
            'From: ' . get_bloginfo('name') . ' <' . get_option('admin_email') . '>',
        );
        
        // Registro l'invio nel log di WordPress
        error_log(sprintf('WAA: Tentativo di invio email di registrazione assicurazione a %s per l\'ordine #%s', $to, $order->get_order_number()));
        
        // Invio l'email
        $sent = wp_mail($to, $subject, $message, $headers);
        
        if (!$sent) {
            error_log(sprintf('WAA: Errore nell\'invio email di registrazione assicurazione a %s per l\'ordine #%s', $to, $order->get_order_number()));
        }
        
        return $sent;
    }

    /**
     * Invia l'email di utilizzo dell'assicurazione
     *
     * @param WC_Order $order Oggetto ordine
     * @param array $insurance_data Dati dell'assicurazione utilizzata
     * @return bool Esito dell'invio
     */
    public static function send_usage_email($order, $insurance_data) {
        if (!$order || !is_a($order, 'WC_Order') || empty($insurance_data['customer_email'])) {
            error_log('WAA_Email: Impossibile inviare email di utilizzo - dati mancanti o non validi');
            return false;
        }

        $to = $insurance_data['customer_email'];
        $subject = sprintf(
            __('[%s] Utilizzo Assicurazione - Ordine #%s', 'woo-attiva-assicurazione'),
            get_bloginfo('name'),
            $order->get_order_number()
        );
        
        ob_start();
        
        // Includo l'header dell'email
        $header_template = locate_template('woocommerce/emails/email-header.php');
        if (!empty($header_template)) {
            include $header_template;
        } else {
            wc_get_template('emails/email-header.php', array('email_heading' => $subject));
        }
        
        // Contenuto dell'email
        ?>
        <p><?php printf(
            __('Gentile %s,', 'woo-attiva-assicurazione'),
            esc_html($insurance_data['customer_name'])
        ); ?></p>
        
        <p><?php _e('Ti informiamo che la tua assicurazione è stata utilizzata. Di seguito trovi i dettagli:', 'woo-attiva-assicurazione'); ?></p>
        
        <div class="waa-insurance-info">
            <h2 class="waa-insurance-heading"><?php _e('Dettagli Assicurazione', 'woo-attiva-assicurazione'); ?></h2>
            
            <p><strong><?php _e('Ordine:', 'woo-attiva-assicurazione'); ?></strong> #<?php echo esc_html($order->get_order_number()); ?></p>
            <p><strong><?php _e('Prodotto:', 'woo-attiva-assicurazione'); ?></strong> <?php echo esc_html($insurance_data['product_name']); ?></p>
            <p><strong><?php _e('IMEI:', 'woo-attiva-assicurazione'); ?></strong> <?php echo esc_html($insurance_data['imei']); ?></p>
            <p><strong><?php _e('Data di Attivazione:', 'woo-attiva-assicurazione'); ?></strong> <?php echo esc_html(date_i18n(get_option('date_format'), strtotime($insurance_data['activation_date']))); ?></p>
            <p><strong><?php _e('Data di Utilizzo:', 'woo-attiva-assicurazione'); ?></strong> <?php echo esc_html(date_i18n(get_option('date_format'), strtotime($insurance_data['usage_date']))); ?></p>
            <p><strong><?php _e('Motivazione:', 'woo-attiva-assicurazione'); ?></strong> <?php echo esc_html($insurance_data['usage_reason']); ?></p>
            <p><strong><?php _e('Stato:', 'woo-attiva-assicurazione'); ?></strong> <?php _e('Utilizzata', 'woo-attiva-assicurazione'); ?></p>
        </div>
        
        <p><?php _e('Conserva questa email come ricevuta dell\'utilizzo della tua assicurazione.', 'woo-attiva-assicurazione'); ?></p>
        
        <p><?php _e('In caso di domande o necessità di supporto, non esitare a contattarci.', 'woo-attiva-assicurazione'); ?></p>
        
        <?php
        
        // Riepilogo dell'ordine (opzionale)
        do_action('woocommerce_email_order_details', $order, false, false, $to);
        
        // Include il footer dell'email
        $footer_template = locate_template('woocommerce/emails/email-footer.php');
        if (!empty($footer_template)) {
            include $footer_template;
        } else {
            wc_get_template('emails/email-footer.php');
        }
        
        $message = ob_get_clean();
        
        // Imposto gli headers per l'email HTML
        $headers = array(
            'Content-Type: text/html; charset=UTF-8',
            'From: ' . get_bloginfo('name') . ' <' . get_option('admin_email') . '>',
        );
        
        // Registro l'invio nel log di WordPress
        error_log(sprintf('WAA: Tentativo di invio email di utilizzo assicurazione a %s per l\'ordine #%s', $to, $order->get_order_number()));
        
        // Invio l'email
        $sent = wp_mail($to, $subject, $message, $headers);
        
        if (!$sent) {
            error_log(sprintf('WAA: Errore nell\'invio email di utilizzo assicurazione a %s per l\'ordine #%s', $to, $order->get_order_number()));
        }
        
        return $sent;
    }
}

// Inizializza la classe
WAA_Email::init(); 
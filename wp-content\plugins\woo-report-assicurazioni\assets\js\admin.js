/**
 * Admin JavaScript for Report Assicurazioni plugin
 */

(function($) {
    'use strict';

    $(document).ready(function() {
        // Initialize the admin functionality
        ReportAssicurazioni.init();
    });

    var ReportAssicurazioni = {
        
        /**
         * Initialize the admin functionality
         */
        init: function() {
            this.bindEvents();
            this.initDatePickers();
            this.initTableSorting();
        },

        /**
         * Bind event handlers
         */
        bindEvents: function() {
            // PDF Export button
            $('#export-pdf').on('click', this.handlePdfExport);
            
            // Form validation
            $('.report-filters form').on('submit', this.validateForm);
            
            // Clear filters button
            $(document).on('click', '.clear-filters', this.clearFilters);
            
            // Auto-submit form when date changes (optional)
            $('#start_date, #end_date').on('change', function() {
                if ($(this).closest('form').find('#auto_submit').is(':checked')) {
                    $(this).closest('form').submit();
                }
            });
        },

        /**
         * Initialize date pickers with validation
         */
        initDatePickers: function() {
            var today = new Date().toISOString().split('T')[0];
            
            // Set max date to today
            $('#start_date, #end_date').attr('max', today);
            
            // Validate date range
            $('#start_date').on('change', function() {
                var startDate = $(this).val();
                var endDate = $('#end_date').val();
                
                if (startDate && endDate && startDate > endDate) {
                    $('#end_date').val(startDate);
                }
                
                $('#end_date').attr('min', startDate);
            });
            
            $('#end_date').on('change', function() {
                var endDate = $(this).val();
                var startDate = $('#start_date').val();
                
                if (startDate && endDate && endDate < startDate) {
                    $('#start_date').val(endDate);
                }
                
                $('#start_date').attr('max', endDate);
            });
        },

        /**
         * Initialize table sorting (if needed)
         */
        initTableSorting: function() {
            // Add sorting functionality to table headers
            $('.wp-list-table th').each(function() {
                if (!$(this).hasClass('no-sort')) {
                    $(this).css('cursor', 'pointer');
                    $(this).on('click', function() {
                        ReportAssicurazioni.sortTable($(this));
                    });
                }
            });
        },

        /**
         * Sort table by column
         */
        sortTable: function($header) {
            var table = $header.closest('table');
            var columnIndex = $header.index();
            var rows = table.find('tbody tr').get();
            var isAscending = !$header.hasClass('sorted-asc');
            
            // Remove existing sort classes
            table.find('th').removeClass('sorted-asc sorted-desc');
            
            // Add sort class to current header
            $header.addClass(isAscending ? 'sorted-asc' : 'sorted-desc');
            
            // Sort rows
            rows.sort(function(a, b) {
                var aText = $(a).find('td').eq(columnIndex).text().trim();
                var bText = $(b).find('td').eq(columnIndex).text().trim();
                
                // Handle numeric values (like order numbers and amounts)
                if (columnIndex === 0) { // Order number
                    aText = parseInt(aText.replace(/[^0-9]/g, '')) || 0;
                    bText = parseInt(bText.replace(/[^0-9]/g, '')) || 0;
                } else if (columnIndex === 3) { // Amount
                    aText = parseFloat(aText.replace(/[^0-9.-]/g, '')) || 0;
                    bText = parseFloat(bText.replace(/[^0-9.-]/g, '')) || 0;
                } else if (columnIndex === 1) { // Date
                    aText = new Date(aText);
                    bText = new Date(bText);
                }
                
                if (aText < bText) return isAscending ? -1 : 1;
                if (aText > bText) return isAscending ? 1 : -1;
                return 0;
            });
            
            // Reorder table rows
            $.each(rows, function(index, row) {
                table.find('tbody').append(row);
            });
        },

        /**
         * Handle PDF export
         */
        handlePdfExport: function(e) {
            e.preventDefault();

            var $button = $(this);

            // Check if button is disabled
            if ($button.prop('disabled') || $button.is(':disabled')) {
                return false;
            }

            // Check if any filters are applied
            var hasFilters = $('#start_date').val() || $('#end_date').val() || $('#search').val();
            if (!hasFilters) {
                ReportAssicurazioni.showMessage('error', reportAssicurazioni.strings.no_filters_error || 'Please apply filters before exporting.');
                return false;
            }

            var originalText = $button.text();

            // Show loading state
            $button.addClass('loading');
            $button.text(reportAssicurazioni.strings.exporting);
            $button.prop('disabled', true);
            
            // Get current filter values
            var data = {
                action: 'export_report_pdf',
                nonce: reportAssicurazioni.nonce,
                start_date: $('#start_date').val(),
                end_date: $('#end_date').val(),
                search: $('#search').val()
            };
            
            // Create a form and submit it to trigger download
            var form = $('<form>', {
                method: 'POST',
                action: reportAssicurazioni.ajax_url,
                target: '_blank'
            });
            
            $.each(data, function(key, value) {
                form.append($('<input>', {
                    type: 'hidden',
                    name: key,
                    value: value
                }));
            });
            
            $('body').append(form);
            form.submit();
            form.remove();
            
            // Reset button state after a delay
            setTimeout(function() {
                $button.removeClass('loading');
                $button.text(originalText);
                $button.prop('disabled', false);
            }, 2000);
        },

        /**
         * Validate form before submission
         */
        validateForm: function(e) {
            var startDate = $('#start_date').val();
            var endDate = $('#end_date').val();
            var search = $('#search').val();
            var hasError = false;

            // Remove existing error messages
            $('.report-message.error').remove();

            // Check if any filter is applied
            if (!startDate && !endDate && !search) {
                ReportAssicurazioni.showMessage('error', 'Please apply at least one filter to search for orders.');
                hasError = true;
            }

            // Validate date range
            if (startDate && endDate && startDate > endDate) {
                ReportAssicurazioni.showMessage('error', reportAssicurazioni.strings.date_error);
                hasError = true;
            }

            // Validate date not in future
            var today = new Date().toISOString().split('T')[0];
            if (startDate && startDate > today) {
                ReportAssicurazioni.showMessage('error', reportAssicurazioni.strings.future_date_error);
                hasError = true;
            }

            if (endDate && endDate > today) {
                ReportAssicurazioni.showMessage('error', reportAssicurazioni.strings.future_date_error);
                hasError = true;
            }

            if (hasError) {
                e.preventDefault();
                return false;
            }

            // Show loading state
            ReportAssicurazioni.showLoading();

            return true;
        },

        /**
         * Clear all filters
         */
        clearFilters: function(e) {
            e.preventDefault();
            
            $('#start_date, #end_date, #search').val('');
            $('.report-filters form').submit();
        },

        /**
         * Show message to user
         */
        showMessage: function(type, message) {
            var $message = $('<div>', {
                class: 'report-message ' + type,
                text: message
            });
            
            $('.wrap h1').after($message);
            
            // Auto-hide success messages
            if (type === 'success') {
                setTimeout(function() {
                    $message.fadeOut();
                }, 5000);
            }
        },

        /**
         * Show loading state
         */
        showLoading: function() {
            var $tbody = $('.report-table-container tbody');
            var $loading = $('<tr><td colspan="4" class="report-loading"><div class="spinner is-active"></div><p>' +
                           (reportAssicurazioni.strings.loading || 'Loading report data...') + '</p></td></tr>');

            $tbody.html($loading);
        },

        /**
         * Hide loading state
         */
        hideLoading: function() {
            $('.report-loading').remove();
        }
    };

    // Make ReportAssicurazioni available globally
    window.ReportAssicurazioni = ReportAssicurazioni;

})(jQuery);

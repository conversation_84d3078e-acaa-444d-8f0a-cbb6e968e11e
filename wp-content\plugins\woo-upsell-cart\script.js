jQuery(document).ready(function($) {

    // Gestione del pulsante "Aggiungi al carrello"
    $(document).on('click', '.upsell-add-to-cart-btn[data-action="add-to-cart"]', function(e) {
        e.preventDefault();

        const $button = $(this);
        const productId = $button.data('product-id');
        const $buttonText = $button.find('.button-text');
        const $loadingSpinner = $button.find('.loading-spinner');

        // Disabilita il pulsante e mostra loading
        $button.prop('disabled', true);
        $buttonText.hide();
        $loadingSpinner.show();

        // Effettua la richiesta AJAX
        $.ajax({
            url: upsell_ajax.ajax_url,
            type: 'POST',
            data: {
                action: 'upsell_add_to_cart',
                product_id: productId,
                quantity: 1,
                nonce: upsell_ajax.nonce
            },
            success: function(response) {
                // WooCommerce get_refreshed_fragments() restituisce direttamente i fragments
                if (response && (response.fragments || response.success !== false)) {
                    // Aggiorna i frammenti del carrello
                    if (response.fragments) {
                        $.each(response.fragments, function(key, value) {
                            $(key).replaceWith(value);
                        });
                    }

                    // Mostra feedback di successo
                    $button.removeClass('upsell-add-to-cart-btn').addClass('upsell-added-to-cart');
                    $loadingSpinner.hide();
                    $buttonText.text('✓ Aggiunto').show();

                    // Trigger evento WooCommerce per aggiornare il carrello
                    $(document.body).trigger('added_to_cart', [response.fragments, response.cart_hash, $button]);

                    // Rimuovi automaticamente la card dopo 2 secondi
                    setTimeout(function() {
                        const card = $button.closest('.upsell-product-card')[0];
                        if (card) {
                            removeUpsellCard(card.querySelector('.upsell-close-btn'));
                        }
                    }, 2000);

                } else {
                    // Gestisci errore
                    let errorMessage = 'Errore durante l\'aggiunta al carrello';

                    if (response && response.data && response.data.message) {
                        errorMessage = response.data.message;
                    } else if (response && response.message) {
                        errorMessage = response.message;
                    }

                    showUpsellError($button, errorMessage);
                }
            },
            error: function(xhr, status, error) {
                console.error('AJAX Error:', status, error);
                showUpsellError($button, 'Errore di connessione');
            }
        });
    });

    // Funzione per mostrare errori
    function showUpsellError($button, message) {
        const $buttonText = $button.find('.button-text');
        const $loadingSpinner = $button.find('.loading-spinner');

        $loadingSpinner.hide();
        $buttonText.text('Errore').show();
        $button.prop('disabled', false);

        // Ripristina il testo originale dopo 3 secondi
        setTimeout(function() {
            $buttonText.text('Aggiungi al carrello');
        }, 3000);

        console.error('Upsell Add to Cart Error:', message);
    }
});

// JavaScript per la funzionalità di chiusura delle card upsell
function removeUpsellCard(button) {
    const card = button.closest('.upsell-product-card');
    if (card) {
        // Animazione di fade out
        card.style.transition = 'opacity 0.3s ease, transform 0.3s ease';
        card.style.opacity = '0';
        card.style.transform = 'translateX(20px)';

        // Rimuovi l'elemento dopo l'animazione
        setTimeout(() => {
            card.remove();

            // Controlla se ci sono ancora card visibili
            const container = document.querySelector('.upsell-products-container');
            const remainingCards = container.querySelectorAll('.upsell-product-card');

            // Se non ci sono più card, nascondi l'intera sezione upsell
            if (remainingCards.length === 0) {
                const upsellSection = document.querySelector('.upsell-carrello-custom');
                if (upsellSection) {
                    upsellSection.style.transition = 'opacity 0.3s ease';
                    upsellSection.style.opacity = '0';
                    setTimeout(() => {
                        upsellSection.style.display = 'none';
                    }, 300);
                }
            }
        }, 300);
    }
}

// Assicurati che la funzione sia disponibile globalmente
window.removeUpsellCard = removeUpsellCard;

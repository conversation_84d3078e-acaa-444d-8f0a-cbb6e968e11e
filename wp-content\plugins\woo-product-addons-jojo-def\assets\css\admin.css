/**
 * Stili CSS per la parte amministrativa del plugin
 */

/* Contenitore dei prodotti accessori */
.product_accessories_container {
    margin-top: 10px;
    border: 1px solid #ddd;
    padding: 10px;
    max-height: 300px;
    overflow-y: auto;
}

/* Prodotto selezionato */
.selected-product {
    display: flex;
    align-items: center;
    margin-bottom: 10px;
    padding: 10px;
    border: 1px solid #eee;
    background-color: #f9f9f9;
    border-radius: 4px;
}

.selected-product:last-child {
    margin-bottom: 0;
}

/* Immagine del prodotto */
.selected-product .product-image {
    flex: 0 0 50px;
    margin-right: 10px;
}

.selected-product .product-image img {
    max-width: 100%;
    height: auto;
    border-radius: 3px;
}

/* Informazioni del prodotto */
.selected-product .product-info {
    flex: 1;
    display: flex;
    flex-direction: column;
}

.selected-product .product-name {
    font-weight: bold;
    margin-bottom: 5px;
}

.selected-product .product-price {
    color: #777;
}

/* Pulsante di rimozione */
.selected-product .remove-product {
    color: #a00;
    text-decoration: none;
    font-size: 18px;
    font-weight: bold;
    margin-left: 10px;
}

.selected-product .remove-product:hover {
    color: #dc3232;
}

/* Stili per l'autocomplete */
.ui-autocomplete {
    max-height: 300px;
    overflow-y: auto;
    overflow-x: hidden;
    padding: 0;
    border: 1px solid #aaa;
    background-color: #fff;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
    z-index: 100000;
}

.ui-autocomplete .ui-menu-item {
    padding: 0;
    border: none;
}

.ui-autocomplete .ui-menu-item-wrapper {
    padding: 8px 10px;
    border: none;
    display: flex;
    align-items: center;
}

.ui-autocomplete .ui-menu-item-wrapper.ui-state-active {
    margin: 0;
    background-color: #f0f0f0;
    border: none;
    color: #000;
}

.ui-autocomplete .product-suggestion-image {
    flex: 0 0 40px;
    margin-right: 10px;
}

.ui-autocomplete .product-suggestion-image img {
    max-width: 100%;
    height: auto;
    border-radius: 3px;
}

.ui-autocomplete .product-suggestion-info {
    flex: 1;
}

.ui-autocomplete .product-suggestion-name {
    font-weight: bold;
    margin-bottom: 3px;
}

.ui-autocomplete .product-suggestion-price {
    font-size: 12px;
    color: #777;
} 
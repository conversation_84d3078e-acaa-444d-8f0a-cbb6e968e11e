(()=>{var e,t,o,r={6896:(e,t,o)=>{"use strict";const r=window.wp.blocks;var s=o(4530),n=o(2108),l=o(4921);const a=window.wp.blockEditor;var c=o(7723);const i=window.wp.components;var u=o(195),d=o(2174),p=(o(5765),o(790));const m=({className:e,rating:t,ratedProductsCount:o})=>{const r=(0,l.A)("wc-block-components-product-rating",e),s={width:t/5*100+"%"},n=(0,c.sprintf)(/* translators: %f is referring to the average rating value */ /* translators: %f is referring to the average rating value */
(0,c.__)("Rated %f out of 5","woocommerce"),t),a={__html:(0,c.sprintf)(/* translators: %s is the rating value wrapped in HTML strong tags. */ /* translators: %s is the rating value wrapped in HTML strong tags. */
(0,c.__)("Rated %s out of 5","woocommerce"),(0,c.sprintf)('<strong class="rating">%f</strong>',t))};return(0,p.jsxs)("div",{className:r,children:[(0,p.jsx)("div",{className:"wc-block-components-product-rating__stars",role:"img","aria-label":n,children:(0,p.jsx)("span",{style:s,dangerouslySetInnerHTML:a})}),null!==o?(0,p.jsxs)("span",{className:"wc-block-components-product-rating-count",children:["(",o,")"]}):null]})};var w=o(6087),g=o(923),f=o.n(g);function b(e){const t=(0,w.useRef)(e);return f()(e,t.current)||(t.current=e),t.current}const h=window.wc.wcBlocksData,_=window.wp.data,y=(0,w.createContext)("page"),v=()=>(0,w.useContext)(y),x=(y.Provider,e=>{const t=v();e=e||t;const o=(0,_.useSelect)((t=>t(h.QUERY_STATE_STORE_KEY).getValueForQueryContext(e,void 0)),[e]),{setValueForQueryContext:r}=(0,_.useDispatch)(h.QUERY_STATE_STORE_KEY);return[o,(0,w.useCallback)((t=>{r(e,t)}),[e,r])]}),k=(e,t,o)=>{const r=v();o=o||r;const s=(0,_.useSelect)((r=>r(h.QUERY_STATE_STORE_KEY).getValueForQueryKey(o,e,t)),[o,e]),{setQueryValue:n}=(0,_.useDispatch)(h.QUERY_STATE_STORE_KEY);return[s,(0,w.useCallback)((t=>{n(o,e,t)}),[o,e,n])]};var j=o(4347);const C=window.wc.wcTypes;var S=o(9456);const E=({queryAttribute:e,queryPrices:t,queryStock:o,queryRating:r,queryState:s,isEditor:n=!1})=>{let l=v();l=`${l}-collection-data`;const[a]=x(l),[c,i]=k("calculate_attribute_counts",[],l),[u,d]=k("calculate_price_range",null,l),[p,m]=k("calculate_stock_status_counts",null,l),[g,f]=k("calculate_rating_counts",null,l),y=b(e||{}),E=b(t),N=b(o),T=b(r);(0,w.useEffect)((()=>{"object"==typeof y&&Object.keys(y).length&&(c.find((e=>(0,C.objectHasProp)(y,"taxonomy")&&e.taxonomy===y.taxonomy))||i([...c,y]))}),[y,c,i]),(0,w.useEffect)((()=>{u!==E&&void 0!==E&&d(E)}),[E,d,u]),(0,w.useEffect)((()=>{p!==N&&void 0!==N&&m(N)}),[N,m,p]),(0,w.useEffect)((()=>{g!==T&&void 0!==T&&f(T)}),[T,f,g]);const[R,P]=(0,w.useState)(n),[A]=(0,j.d7)(R,200);R||P(!0);const O=(0,w.useMemo)((()=>(e=>{const t=e;return Array.isArray(e.calculate_attribute_counts)&&(t.calculate_attribute_counts=(0,S.di)(e.calculate_attribute_counts.map((({taxonomy:e,queryType:t})=>({taxonomy:e,query_type:t})))).asc(["taxonomy","query_type"])),t})(a)),[a]),{results:B,isLoading:L}=(e=>{const{namespace:t,resourceName:o,resourceValues:r=[],query:s={},shouldSelect:n=!0}=e;if(!t||!o)throw new Error("The options object must have valid values for the namespace and the resource properties.");const l=(0,w.useRef)({results:[],isLoading:!0}),a=b(s),c=b(r),i=(()=>{const[,e]=(0,w.useState)();return(0,w.useCallback)((t=>{e((()=>{throw t}))}),[])})(),u=(0,_.useSelect)((e=>{if(!n)return null;const r=e(h.COLLECTIONS_STORE_KEY),s=[t,o,a,c],l=r.getCollectionError(...s);if(l){if(!(0,C.isError)(l))throw new Error("TypeError: `error` object is not an instance of Error constructor");i(l)}return{results:r.getCollection(...s),isLoading:!r.hasFinishedResolution("getCollection",s)}}),[t,o,c,a,n,i]);return null!==u&&(l.current=u),l.current})({namespace:"/wc/store/v1",resourceName:"products/collection-data",query:{...s,page:void 0,per_page:void 0,orderby:void 0,order:void 0,...O},shouldSelect:A});return{data:B,isLoading:L}},N=window.wc.wcSettings,T=window.wc.blocksComponents;o(874);const R=({className:e,isLoading:t,disabled:o,
/* translators: Submit button text for filters. */
label:r=(0,c.__)("Apply","woocommerce"),onClick:s,screenReaderLabel:n=(0,c.__)("Apply filter","woocommerce")})=>(0,p.jsx)("button",{type:"submit",className:(0,l.A)("wp-block-button__link","wc-block-filter-submit-button","wc-block-components-filter-submit-button",{"is-loading":t},e),disabled:o,onClick:s,children:(0,p.jsx)(T.Label,{label:r,screenReaderLabel:n})});o(7165);const P=({className:e,
/* translators: Reset button text for filters. */
label:t=(0,c.__)("Reset","woocommerce"),onClick:o,screenReaderLabel:r=(0,c.__)("Reset filter","woocommerce")})=>(0,p.jsx)("button",{className:(0,l.A)("wc-block-components-filter-reset-button",e),onClick:o,children:(0,p.jsx)(T.Label,{label:t,screenReaderLabel:r})});var A=o(4642);o(4357);const O=({className:e,style:t,suggestions:o,multiple:r=!0,saveTransform:s=e=>e.trim().replace(/\s/g,"-"),messages:n={},validateInput:a=e=>o.includes(e),label:c="",...i})=>(0,p.jsx)("div",{className:(0,l.A)("wc-blocks-components-form-token-field-wrapper",e,{"single-selection":!r}),style:t,children:(0,p.jsx)(A.A,{label:c,__experimentalExpandOnFocus:!0,__experimentalShowHowTo:!1,__experimentalValidateInput:a,saveTransform:s,maxLength:r?void 0:1,suggestions:o,messages:n,...i})}),B=window.wp.url;function L(){return Math.floor(Math.random()*Date.now())}const F=(0,N.getSettingWithCoercion)("isRenderingPhpTemplate",!1,C.isBoolean);function q(e){if(F){const t=new URL(e);t.pathname=t.pathname.replace(/\/page\/[0-9]+/i,""),t.searchParams.delete("paged"),t.searchParams.forEach(((e,o)=>{o.match(/^query(?:-[0-9]+)?-page$/)&&t.searchParams.delete(o)})),window.location.href=t.href}else window.history.replaceState({},"",e)}const D=e=>{const t=(0,B.getQueryArgs)(e);return(0,B.addQueryArgs)(e,t)},M=[{label:(0,p.jsx)(m,{rating:5,ratedProductsCount:null},5),value:"5"},{label:(0,p.jsx)(m,{rating:4,ratedProductsCount:null},4),value:"4"},{label:(0,p.jsx)(m,{rating:3,ratedProductsCount:null},3),value:"3"},{label:(0,p.jsx)(m,{rating:2,ratedProductsCount:null},2),value:"2"},{label:(0,p.jsx)(m,{rating:1,ratedProductsCount:null},1),value:"1"}];o(6121);const Q=e=>e.trim().replace(/\s/g,"-").replace(/_/g,"-").replace(/-+/g,"-").replace(/[^a-zA-Z0-9-]/g,""),I=(0,w.createContext)({}),Y="rating_filter",U=e=>(0,c.sprintf)(/* translators: %s is referring to the average rating value */ /* translators: %s is referring to the average rating value */
(0,c.__)("Rated %s out of 5 filter added.","woocommerce"),e),V=e=>(0,c.sprintf)(/* translators: %s is referring to the average rating value */ /* translators: %s is referring to the average rating value */
(0,c.__)("Rated %s out of 5 filter added.","woocommerce"),e),G=({attributes:e,isEditor:t,noRatingsNotice:o=null})=>{const r=(()=>{const{wrapper:e}=(0,w.useContext)(I);return t=>{e&&e.current&&(e.current.hidden=!t)}})(),n=(0,N.getSettingWithCoercion)("isRenderingPhpTemplate",!1,C.isBoolean),[a,i]=(0,w.useState)(!1),[g]=x(),{data:h,isLoading:_}=E({queryRating:!0,queryState:g,isEditor:t}),[y,v]=(0,w.useState)(e.isPreview?M:[]),j=!e.isPreview&&_&&0===y.length,S=!e.isPreview&&_,A=(0,w.useMemo)((()=>((e="filter_rating")=>{const t=(o=e,window?(0,B.getQueryArg)(window.location.href,o):null);var o;return t?(0,C.isString)(t)?t.split(","):t:[]})("rating_filter")),[]),[F,G]=(0,w.useState)(A),[K,W]=k("rating",A),[$,H]=(0,w.useState)(L()),[J,z]=(0,w.useState)(!1),X="single"!==e.selectType,Z=X?!j&&F.length<y.length:!j&&0===F.length,ee=(0,w.useCallback)((e=>{t||(e&&!n&&W(e),(e=>{if(!window)return;if(0===e.length){const e=(0,B.removeQueryArgs)(window.location.href,Y);return void(e!==D(window.location.href)&&q(e))}const t=(0,B.addQueryArgs)(window.location.href,{[Y]:e.join(",")});t!==D(window.location.href)&&q(t)})(e))}),[t,W,n]);(0,w.useEffect)((()=>{e.showFilterButton||ee(F)}),[e.showFilterButton,F,ee]);const te=b((0,w.useMemo)((()=>K),[K])),oe=function(e,t){const o=(0,w.useRef)();return(0,w.useEffect)((()=>{o.current===e||(o.current=e)}),[e,t]),o.current}(te);(0,w.useEffect)((()=>{f()(oe,te)||f()(F,te)||G(te)}),[F,te,oe]),(0,w.useEffect)((()=>{a||(W(A),i(!0))}),[W,a,i,A]),(0,w.useEffect)((()=>{if(_||e.isPreview)return;const o=!_&&(0,C.objectHasProp)(h,"rating_counts")&&Array.isArray(h.rating_counts)?[...h.rating_counts].reverse():[];if(t&&0===o.length)return v(M),void z(!0);const r=o.filter((e=>(0,C.isObject)(e)&&Object.keys(e).length>0)).map((t=>({label:(0,p.jsx)(m,{rating:t?.rating,ratedProductsCount:e.showCounts?t?.count:null},t?.rating),value:t?.rating?.toString()})));v(r),H(L())}),[e.showCounts,e.isPreview,h,_,K,t]);const re=(0,w.useCallback)((e=>{const t=F.includes(e);if(!X){const o=t?[]:[e];return(0,u.speak)(t?V(e):U(e)),void G(o)}if(t){const t=F.filter((t=>t!==e));return(0,u.speak)(V(e)),void G(t)}const o=[...F,e].sort(((e,t)=>Number(t)-Number(e)));(0,u.speak)(U(e)),G(o)}),[F,X]);return(_||0!==y.length)&&(0,N.getSettingWithCoercion)("hasFilterableProducts",!1,C.isBoolean)?(r(!0),(0,p.jsxs)(p.Fragment,{children:[J&&o,(0,p.jsx)("div",{className:(0,l.A)("wc-block-rating-filter",`style-${e.displayStyle}`,{"is-loading":j}),children:"dropdown"===e.displayStyle?(0,p.jsxs)(p.Fragment,{children:[(0,p.jsx)(O,{className:(0,l.A)({"single-selection":!X,"is-loading":j}),style:{borderStyle:"none"},suggestions:y.filter((e=>!F.includes(e.value))).map((e=>e.value)),disabled:j,placeholder:(0,c.__)("Select Rating","woocommerce"),onChange:e=>{!X&&e.length>1&&(e=[e[e.length-1]]);const t=[e=e.map((e=>{const t=y.find((t=>t.value===e));return t?t.value:e})),F].reduce(((e,t)=>e.filter((e=>!t.includes(e)))));if(1===t.length)return re(t[0]);const o=[F,e].reduce(((e,t)=>e.filter((e=>!t.includes(e)))));1===o.length&&re(o[0])},value:F,displayTransform:e=>{const t={value:e,label:(0,p.jsx)(m,{rating:Number(e),ratedProductsCount:0},Number(e))},o=y.find((t=>t.value===e))||t,{label:r,value:s}=o;return Object.assign({},r,{toLocaleLowerCase:()=>s,substring:(e,t)=>0===e&&1===t?r:""})},saveTransform:Q,messages:{added:(0,c.__)("Rating filter added.","woocommerce"),removed:(0,c.__)("Rating filter removed.","woocommerce"),remove:(0,c.__)("Remove rating filter.","woocommerce"),__experimentalInvalid:(0,c.__)("Invalid rating filter.","woocommerce")}},$),Z&&(0,p.jsx)(s.A,{icon:d.A,size:30})]}):(0,p.jsx)(T.CheckboxList,{className:"wc-block-rating-filter-list",options:y,checked:F,onChange:e=>{re(e.toString())},isLoading:j,isDisabled:S})}),(0,p.jsxs)("div",{className:"wc-block-rating-filter__actions",children:[(F.length>0||t)&&!j&&(0,p.jsx)(P,{onClick:()=>{G([]),W([]),ee([])},screenReaderLabel:(0,c.__)("Reset rating filter","woocommerce")}),e.showFilterButton&&(0,p.jsx)(R,{className:"wc-block-rating-filter__button",isLoading:j,disabled:j||S,onClick:()=>ee(F),screenReaderLabel:(0,c.__)("Apply rating filter","woocommerce")})]})]})):(r(!1),null)};function K({children:e,className:t,actionLabel:o,onActionClick:r,...s}){return(0,p.jsx)(i.Notice,{...s,className:(0,l.$)("wc-block-editor-components-upgrade-downgrade-notice",t),actions:[{label:o,onClick:r,noDefaultClasses:!0,variant:"link"}],children:(0,p.jsx)("div",{className:"wc-block-editor-components-upgrade-downgrade-notice__text",children:e})})}o(2516),o(9969);const W=({clientId:e})=>{const{replaceBlock:t,removeBlock:o,updateBlockAttributes:s,selectBlock:n}=(0,_.useDispatch)("core/block-editor"),l=(0,w.createInterpolateElement)((0,c.__)("Upgrade all Filter blocks on this page for better performance and more customizability","woocommerce"),{strongText:(0,p.jsx)("strong",{children:(0,c.__)("Product Filters","woocommerce")})}),a=(0,c.__)("Upgrade all Filter blocks","woocommerce");return(0,p.jsx)(K,{isDismissible:!1,actionLabel:a,onActionClick:()=>{const{getBlocksByName:l,getBlockParentsByBlockName:a}=(0,_.select)("core/block-editor"),c=a(e,"woocommerce/filter-wrapper"),i=(0,r.createBlock)("woocommerce/product-filters");c.length?t(c[0],i):t(e,i),l("woocommerce/filter-wrapper").forEach((e=>{s(e,{lock:{remove:!1}}),o(e)})),["woocommerce/active-filters","woocommerce/price-filter","woocommerce/attribute-filter","woocommerce/stock-filter"].forEach((e=>{const t=l(e);t.length&&(s(t[0],{lock:{remove:!1}}),o(t[0]))})),n(i.clientId)},children:l})},$=(0,p.jsx)(i.Notice,{status:"warning",isDismissible:!1,children:(0,p.jsx)("p",{children:(0,c.__)("Your store doesn't have any products with ratings yet. This filter option will display when a product receives a review.","woocommerce")})}),H=(0,i.withSpokenMessages)((({attributes:e,setAttributes:t,clientId:o})=>{const{className:r,displayStyle:s,showCounts:n,showFilterButton:u,selectType:d}=e,m=(0,a.useBlockProps)({className:(0,l.A)("wc-block-rating-filter",r)});return(0,p.jsxs)(p.Fragment,{children:[(0,p.jsxs)(a.InspectorControls,{children:[(0,p.jsx)(i.PanelBody,{children:(0,p.jsx)(W,{clientId:o})}),(0,p.jsxs)(i.PanelBody,{title:(0,c.__)("Display Settings","woocommerce"),children:[(0,p.jsx)(i.ToggleControl,{label:(0,c.__)("Display product count","woocommerce"),checked:n,onChange:()=>t({showCounts:!n})}),(0,p.jsxs)(i.__experimentalToggleGroupControl,{label:(0,c.__)("Allow selecting multiple options?","woocommerce"),isBlock:!0,value:d||"multiple",onChange:e=>t({selectType:e}),className:"wc-block-attribute-filter__multiple-toggle",children:[(0,p.jsx)(i.__experimentalToggleGroupControlOption,{value:"multiple",label:(0,c._x)("Multiple","Number of filters","woocommerce")}),(0,p.jsx)(i.__experimentalToggleGroupControlOption,{value:"single",label:(0,c._x)("Single","Number of filters","woocommerce")})]}),(0,p.jsxs)(i.__experimentalToggleGroupControl,{label:(0,c.__)("Display Style","woocommerce"),isBlock:!0,value:s,onChange:e=>t({displayStyle:e}),className:"wc-block-attribute-filter__display-toggle",children:[(0,p.jsx)(i.__experimentalToggleGroupControlOption,{value:"list",label:(0,c.__)("List","woocommerce")}),(0,p.jsx)(i.__experimentalToggleGroupControlOption,{value:"dropdown",label:(0,c.__)("Dropdown","woocommerce")})]}),(0,p.jsx)(i.ToggleControl,{label:(0,c.__)("Show 'Apply filters' button","woocommerce"),help:(0,c.__)("Products will update when the button is clicked.","woocommerce"),checked:u,onChange:e=>t({showFilterButton:e})})]})]},"inspector"),(0,p.jsx)("div",{...m,children:(0,p.jsx)(i.Disabled,{children:(0,p.jsx)(G,{attributes:e,isEditor:!0,noRatingsNotice:$})})})]})})),J=JSON.parse('{"name":"woocommerce/rating-filter","title":"Filter by Rating Controls","description":"Enable customers to filter the product grid by rating.","category":"woocommerce","keywords":["WooCommerce"],"supports":{"interactivity":{"clientNavigation":false},"html":false,"multiple":false,"color":{"background":true,"text":true,"button":true},"inserter":false,"lock":false},"attributes":{"className":{"type":"string","default":""},"showCounts":{"type":"boolean","default":false},"displayStyle":{"type":"string","default":"list"},"showFilterButton":{"type":"boolean","default":false},"selectType":{"type":"string","default":"multiple"},"isPreview":{"type":"boolean","default":false}},"textdomain":"woocommerce","apiVersion":3,"$schema":"https://schemas.wp.org/trunk/block.json"}'),z=[{attributes:{...J.attributes,showCounts:{type:"boolean",default:!0}},save:({attributes:e})=>{const{className:t,showCounts:o}=e,r={"data-show-counts":o};return(0,p.jsx)("div",{...a.useBlockProps.save({className:(0,l.A)("is-loading",t)}),...r,children:(0,p.jsx)("span",{"aria-hidden":!0,className:"wc-block-product-rating-filter__placeholder"})})}}];(0,r.registerBlockType)(J,{icon:{src:(0,p.jsx)(s.A,{icon:n.A,className:"wc-block-editor-components-block-icon"})},attributes:{...J.attributes},edit:H,save({attributes:e}){const{className:t}=e;return(0,p.jsx)("div",{...a.useBlockProps.save({className:(0,l.A)("is-loading",t)})})},deprecated:z})},7165:()=>{},874:()=>{},4357:()=>{},5765:()=>{},2516:()=>{},6121:()=>{},9969:()=>{},1609:e=>{"use strict";e.exports=window.React},790:e=>{"use strict";e.exports=window.ReactJSXRuntime},8468:e=>{"use strict";e.exports=window.lodash},195:e=>{"use strict";e.exports=window.wp.a11y},9491:e=>{"use strict";e.exports=window.wp.compose},4040:e=>{"use strict";e.exports=window.wp.deprecated},8107:e=>{"use strict";e.exports=window.wp.dom},6087:e=>{"use strict";e.exports=window.wp.element},7723:e=>{"use strict";e.exports=window.wp.i18n},923:e=>{"use strict";e.exports=window.wp.isShallowEqual},8558:e=>{"use strict";e.exports=window.wp.keycodes},5573:e=>{"use strict";e.exports=window.wp.primitives},979:e=>{"use strict";e.exports=window.wp.warning}},s={};function n(e){var t=s[e];if(void 0!==t)return t.exports;var o=s[e]={exports:{}};return r[e].call(o.exports,o,o.exports,n),o.exports}n.m=r,e=[],n.O=(t,o,r,s)=>{if(!o){var l=1/0;for(u=0;u<e.length;u++){for(var[o,r,s]=e[u],a=!0,c=0;c<o.length;c++)(!1&s||l>=s)&&Object.keys(n.O).every((e=>n.O[e](o[c])))?o.splice(c--,1):(a=!1,s<l&&(l=s));if(a){e.splice(u--,1);var i=r();void 0!==i&&(t=i)}}return t}s=s||0;for(var u=e.length;u>0&&e[u-1][2]>s;u--)e[u]=e[u-1];e[u]=[o,r,s]},n.n=e=>{var t=e&&e.__esModule?()=>e.default:()=>e;return n.d(t,{a:t}),t},o=Object.getPrototypeOf?e=>Object.getPrototypeOf(e):e=>e.__proto__,n.t=function(e,r){if(1&r&&(e=this(e)),8&r)return e;if("object"==typeof e&&e){if(4&r&&e.__esModule)return e;if(16&r&&"function"==typeof e.then)return e}var s=Object.create(null);n.r(s);var l={};t=t||[null,o({}),o([]),o(o)];for(var a=2&r&&e;"object"==typeof a&&!~t.indexOf(a);a=o(a))Object.getOwnPropertyNames(a).forEach((t=>l[t]=()=>e[t]));return l.default=()=>e,n.d(s,l),s},n.d=(e,t)=>{for(var o in t)n.o(t,o)&&!n.o(e,o)&&Object.defineProperty(e,o,{enumerable:!0,get:t[o]})},n.o=(e,t)=>Object.prototype.hasOwnProperty.call(e,t),n.r=e=>{"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})},n.j=8915,(()=>{var e={8915:0};n.O.j=t=>0===e[t];var t=(t,o)=>{var r,s,[l,a,c]=o,i=0;if(l.some((t=>0!==e[t]))){for(r in a)n.o(a,r)&&(n.m[r]=a[r]);if(c)var u=c(n)}for(t&&t(o);i<l.length;i++)s=l[i],n.o(e,s)&&e[s]&&e[s][0](),e[s]=0;return n.O(u)},o=globalThis.webpackChunkwebpackWcBlocksMainJsonp=globalThis.webpackChunkwebpackWcBlocksMainJsonp||[];o.forEach(t.bind(null,0)),o.push=t.bind(null,o.push.bind(o))})();var l=n.O(void 0,[94],(()=>n(6896)));l=n.O(l),((this.wc=this.wc||{}).blocks=this.wc.blocks||{})["rating-filter"]=l})();
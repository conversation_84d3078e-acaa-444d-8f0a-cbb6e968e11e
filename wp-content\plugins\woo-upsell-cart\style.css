/* Contenitore principale upsell */
.upsell-carrello-custom {
  max-width: 1000px;
  margin: 20px auto;
  padding: 20px;
}

.upsell-carrello-custom h3 {
  font-size: 18px;
  font-weight: 600;
  margin-bottom: 15px;
  color: #333;
}

/* Contenitore delle card prodotti */
.upsell-products-container {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

/* Singola card prodotto */
.upsell-product-card {
  position: relative;
  display: flex;
  align-items: center;
  background: #ffffff;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  padding: 15px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease;
  max-width: 400px;
}

.upsell-product-card:hover {
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
  border-color: #d0d0d0;
}

/* <PERSON><PERSON>sante di ch<PERSON> */
.upsell-close-btn {
  position: absolute;
  top: 8px;
  right: 8px;
  background: none;
  border: none;
  font-size: 18px;
  color: #999;
  cursor: pointer;
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  transition: all 0.2s ease;
  z-index: 10;
}

.upsell-close-btn:hover {
  background-color: #f5f5f5;
  color: #666;
}

.upsell-close-btn span {
  line-height: 1;
  font-weight: normal;
}

/* Immagine prodotto */
.upsell-product-image {
  flex-shrink: 0;
  width: 60px;
  height: 60px;
  margin-right: 15px;
  border-radius: 6px;
  overflow: hidden;
  background-color: #f8f8f8;
}

.upsell-product-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  display: block;
}

.upsell-product-image a {
  display: block;
  width: 100%;
  height: 100%;
}

/* Informazioni prodotto */
.upsell-product-info {
  flex: 1;
  min-width: 0;
}

.upsell-product-title {
  margin: 0 0 8px 0;
  font-size: 16px;
  font-weight: 500;
  line-height: 1.3;
  color: #333;
}

.upsell-product-title a {
  color: inherit;
  text-decoration: none;
  transition: color 0.2s ease;
}

.upsell-product-title a:hover {
  color: #0073aa;
}

/* Prezzo prodotto */
.upsell-product-price {
  font-size: 16px;
  font-weight: 600;
  color: #69a84f;
  margin-bottom: 10px;
}

.upsell-product-price .woocommerce-Price-amount {
  color: inherit;
}

.upsell-product-price .woocommerce-Price-currencySymbol {
  color: inherit;
}

/* Contenitore e pulsante Add to Cart */
.upsell-add-to-cart-container {
  margin-top: 8px;
}

.upsell-add-to-cart-btn {
  background-color: #0073aa;
  color: white;
  border: none;
  padding: 8px 16px;
  border-radius: 4px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  text-decoration: none;
  display: inline-block;
  line-height: 1.2;
  min-width: 120px;
  text-align: center;
}

.upsell-add-to-cart-btn:hover {
  background-color: #005a87;
  transform: translateY(-1px);
}

.upsell-add-to-cart-btn:active {
  transform: translateY(0);
}

.upsell-add-to-cart-btn:disabled {
  background-color: #ccc;
  cursor: not-allowed;
  transform: none;
}

.upsell-add-to-cart-btn.disabled {
  background-color: #e0e0e0;
  color: #999;
  cursor: not-allowed;
}

.upsell-add-to-cart-btn.select-options {
  background-color: #69a84f;
}

.upsell-add-to-cart-btn.select-options:hover {
  background-color: #5a8f42;
}

.upsell-added-to-cart {
  background-color: #46b450 !important;
  cursor: default !important;
}

.upsell-added-to-cart:hover {
  background-color: #46b450 !important;
  transform: none !important;
}

/* Loading spinner */
.loading-spinner {
  display: flex;
  align-items: center;
  gap: 6px;
}

.spinner {
  width: 12px;
  height: 12px;
  border: 2px solid transparent;
  border-top: 2px solid currentColor;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Responsive design */
@media screen and (max-width: 768px) {
  .upsell-carrello-custom {
    margin: 15px auto;
    padding: 15px;
  }

  .upsell-product-card {
    padding: 12px;
    max-width: 100%;
  }

  .upsell-product-image {
    width: 55px;
    height: 55px;
    margin-right: 12px;
  }

  .upsell-product-title {
    font-size: 15px;
  }

  .upsell-product-price {
    font-size: 15px;
  }
}

@media screen and (max-width: 480px) {
  .upsell-product-card {
    padding: 10px;
  }

  .upsell-product-image {
    width: 50px;
    height: 50px;
    margin-right: 10px;
  }

  .upsell-add-to-cart-btn {
    padding: 6px 12px;
    font-size: 13px;
    min-width: 100px;
  }

  .upsell-product-title {
    font-size: 14px;
  }

  .upsell-product-price {
    font-size: 14px;
  }
}

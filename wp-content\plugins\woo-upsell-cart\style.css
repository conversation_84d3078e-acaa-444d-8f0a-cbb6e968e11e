/* Contenitore principale upsell */
.upsell-carrello-custom {
  max-width: 1000px;
  margin: 20px auto;
  padding: 20px;
}

.upsell-carrello-custom h3 {
  font-size: 18px;
  font-weight: 600;
  margin-bottom: 15px;
  color: #333;
}

/* Contenitore delle card prodotti */
.upsell-products-container {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

/* Singola card prodotto */
.upsell-product-card {
  position: relative;
  display: flex;
  align-items: center;
  background: #ffffff;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  padding: 15px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease;
  max-width: 400px;
}

.upsell-product-card:hover {
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
  border-color: #d0d0d0;
}

/* <PERSON><PERSON>sante di ch<PERSON>ura */
.upsell-close-btn {
  position: absolute;
  top: 8px;
  right: 8px;
  background: none;
  border: none;
  font-size: 18px;
  color: #999;
  cursor: pointer;
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  transition: all 0.2s ease;
  z-index: 10;
}

.upsell-close-btn:hover {
  background-color: #f5f5f5;
  color: #666;
}

.upsell-close-btn span {
  line-height: 1;
  font-weight: normal;
}

/* Immagine prodotto */
.upsell-product-image {
  flex-shrink: 0;
  width: 80px;
  height: 80px;
  margin-right: 15px;
  border-radius: 6px;
  overflow: hidden;
  background-color: #f8f8f8;
}

.upsell-product-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  display: block;
}

.upsell-product-image a {
  display: block;
  width: 100%;
  height: 100%;
}

/* Informazioni prodotto */
.upsell-product-info {
  flex: 1;
  min-width: 0;
}

.upsell-product-title {
  margin: 0 0 8px 0;
  font-size: 16px;
  font-weight: 500;
  line-height: 1.3;
  color: #333;
}

.upsell-product-title a {
  color: inherit;
  text-decoration: none;
  transition: color 0.2s ease;
}

.upsell-product-title a:hover {
  color: #0073aa;
}

/* Prezzo prodotto */
.upsell-product-price {
  font-size: 16px;
  font-weight: 600;
  color: #69a84f;
}

.upsell-product-price .woocommerce-Price-amount {
  color: inherit;
}

.upsell-product-price .woocommerce-Price-currencySymbol {
  color: inherit;
}

/* Responsive design */
@media screen and (max-width: 768px) {
  .upsell-carrello-custom {
    margin: 15px auto;
    padding: 15px;
  }

  .upsell-product-card {
    padding: 12px;
    max-width: 100%;
  }

  .upsell-product-image {
    width: 70px;
    height: 70px;
    margin-right: 12px;
  }

  .upsell-product-title {
    font-size: 15px;
  }

  .upsell-product-price {
    font-size: 15px;
  }
}

@media screen and (max-width: 480px) {
  .upsell-product-card {
    padding: 10px;
  }

  .upsell-product-image {
    width: 60px;
    height: 60px;
    margin-right: 10px;
  }

  .upsell-product-title {
    font-size: 14px;
  }

  .upsell-product-price {
    font-size: 14px;
  }
}

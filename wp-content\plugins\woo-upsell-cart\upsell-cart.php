<?php
/**
 * Plugin Name: Upsell nel Carrello
 * Description: Mostra fino a 3 prodotti upsell nella pagina del carrello, esclusi quelli già presenti nel carrello.
 * Version: 1.0
 * Author: <PERSON> Custom
 */

// Aggiunge gli upsell al carrello
add_action('woocommerce_cart_collaterals', 'upsell_nel_carrello_custom');

function upsell_nel_carrello_custom() {
    $carrello = WC()->cart->get_cart();
    $carrello_ids = [];
    $upsell_ids = [];

    foreach ($carrello as $item) {
        $product = $item['data'];
        $carrello_ids[] = $product->get_id();
        $upsells = $product->get_upsell_ids();
        if (!empty($upsells)) {
            $upsell_ids = array_merge($upsell_ids, $upsells);
        }
    }

    // Rimuovi duplicati e quelli già nel carrello
    $upsell_ids = array_unique(array_diff($upsell_ids, $carrello_ids));

    // Limita a massimo 3 prodotti
    $upsell_ids = array_slice($upsell_ids, 0, 3);

    if (!empty($upsell_ids)) {
        echo '<div class="upsell-carrello-custom">';
        echo '<h3>Ti potrebbero interessare:</h3>';
        echo '<div class="upsell-products-container">';

        foreach ($upsell_ids as $product_id) {
            $product = wc_get_product($product_id);
            if ($product) {
                upsell_render_product_card($product);
            }
        }

        echo '</div>';
        echo '</div>';
    }
}

// Funzione per renderizzare una singola card prodotto
function upsell_render_product_card($product) {
    $product_id = $product->get_id();
    $product_name = $product->get_name();
    $product_price = $product->get_price_html();
    $product_image = $product->get_image('thumbnail');
    $product_url = $product->get_permalink();

    ?>
    <div class="upsell-product-card" data-product-id="<?php echo esc_attr($product_id); ?>">
        <button class="upsell-close-btn" onclick="removeUpsellCard(this)" aria-label="Rimuovi prodotto">
            <span>&times;</span>
        </button>

        <div class="upsell-product-image">
            <a href="<?php echo esc_url($product_url); ?>">
                <?php echo $product_image; ?>
            </a>
        </div>

        <div class="upsell-product-info">
            <h4 class="upsell-product-title">
                <a href="<?php echo esc_url($product_url); ?>">
                    <?php echo esc_html($product_name); ?>
                </a>
            </h4>
            <div class="upsell-product-price">
                <?php echo $product_price; ?>
            </div>
        </div>
    </div>
    <?php
}

// Carica il CSS e JavaScript personalizzato
add_action('wp_enqueue_scripts', 'upsell_carrello_custom_assets');
function upsell_carrello_custom_assets() {
    wp_enqueue_style('upsell-carrello-custom-style', plugin_dir_url(__FILE__) . 'style.css');
    wp_enqueue_script('upsell-carrello-custom-script', plugin_dir_url(__FILE__) . 'script.js', array('jquery'), '1.0', true);
}

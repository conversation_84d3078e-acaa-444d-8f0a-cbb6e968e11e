<?php
/**
 * Plugin Name: Upsell nel Carrello
 * Description: Mostra fino a 3 prodotti upsell nella pagina del carrello, esclusi quelli già presenti nel carrello.
 * Version: 1.0
 * Author: <PERSON> Custom
 */

// Aggiunge gli upsell al carrello
add_action('woocommerce_cart_collaterals', 'upsell_nel_carrello_custom');

function upsell_nel_carrello_custom() {
    $carrello = WC()->cart->get_cart();
    $carrello_ids = [];
    $upsell_ids = [];

    foreach ($carrello as $item) {
        $product = $item['data'];
        $carrello_ids[] = $product->get_id();
        $upsells = $product->get_upsell_ids();
        if (!empty($upsells)) {
            $upsell_ids = array_merge($upsell_ids, $upsells);
        }
    }

    // Rimuovi duplicati e quelli già nel carrello
    $upsell_ids = array_unique(array_diff($upsell_ids, $carrello_ids));

    // Limita a massimo 3 prodotti
    $upsell_ids = array_slice($upsell_ids, 0, 3);

    if (!empty($upsell_ids)) {
        echo '<div class="upsell-carrello-custom">';
        echo '<h3>Ti potrebbero interessare:</h3>';
        echo '<div class="upsell-products-container">';

        foreach ($upsell_ids as $product_id) {
            $product = wc_get_product($product_id);
            if ($product) {
                upsell_render_product_card($product);
            }
        }

        echo '</div>';
        echo '</div>';
    }
}

// Funzione per renderizzare una singola card prodotto
function upsell_render_product_card($product) {
    $product_id = $product->get_id();
    $product_name = $product->get_name();
    $product_price = $product->get_price_html();
    $product_image = $product->get_image('thumbnail');
    $product_url = $product->get_permalink();
    $product_type = $product->get_type();
    $is_purchasable = $product->is_purchasable() && $product->is_in_stock();

    // Determina il testo e l'azione del pulsante
    $button_text = 'Aggiungi al carrello';
    $button_class = 'upsell-add-to-cart-btn';
    $button_action = 'add-to-cart';

    if (!$is_purchasable) {
        $button_text = 'Non disponibile';
        $button_class .= ' disabled';
        $button_action = 'disabled';
    } elseif ($product_type === 'variable') {
        $button_text = 'Seleziona opzioni';
        $button_action = 'select-options';
    }

    ?>
    <div class="upsell-product-card" data-product-id="<?php echo esc_attr($product_id); ?>">
        <button class="upsell-close-btn" onclick="removeUpsellCard(this)" aria-label="Rimuovi prodotto">
            <span>&times;</span>
        </button>

        <div class="upsell-product-image">
            <a href="<?php echo esc_url($product_url); ?>">
                <?php echo $product_image; ?>
            </a>
        </div>

        <div class="upsell-product-info">
            <h4 class="upsell-product-title">
                <a href="<?php echo esc_url($product_url); ?>">
                    <?php echo esc_html($product_name); ?>
                </a>
            </h4>
            <div class="upsell-product-price">
                <?php echo $product_price; ?>
            </div>

            <?php if ($is_purchasable || $product_type === 'variable') : ?>
                <div class="upsell-add-to-cart-container">
                    <?php if ($button_action === 'add-to-cart') : ?>
                        <button type="button"
                                class="<?php echo esc_attr($button_class); ?>"
                                data-product-id="<?php echo esc_attr($product_id); ?>"
                                data-product-type="<?php echo esc_attr($product_type); ?>"
                                data-action="add-to-cart">
                            <span class="button-text"><?php echo esc_html($button_text); ?></span>
                            <span class="loading-spinner" style="display: none;">
                                <span class="spinner"></span> Aggiunta...
                            </span>
                        </button>
                    <?php elseif ($button_action === 'select-options') : ?>
                        <a href="<?php echo esc_url($product_url); ?>"
                           class="<?php echo esc_attr($button_class); ?> select-options">
                            <?php echo esc_html($button_text); ?>
                        </a>
                    <?php endif; ?>
                </div>
            <?php endif; ?>
        </div>
    </div>
    <?php
}

// Carica il CSS e JavaScript personalizzato
add_action('wp_enqueue_scripts', 'upsell_carrello_custom_assets');
function upsell_carrello_custom_assets() {
    // Carica solo nelle pagine del carrello
    if (is_cart()) {
        wp_enqueue_style('upsell-carrello-custom-style', plugin_dir_url(__FILE__) . 'style.css', array(), '1.1');

        // Assicurati che gli script WooCommerce siano caricati
        wp_enqueue_script('wc-add-to-cart');
        wp_enqueue_script('woocommerce');

        wp_enqueue_script('upsell-carrello-custom-script', plugin_dir_url(__FILE__) . 'script.js', array('jquery', 'wc-add-to-cart'), '1.1', true);

        // Localizza script per AJAX
        wp_localize_script('upsell-carrello-custom-script', 'upsell_ajax', array(
            'ajax_url' => admin_url('admin-ajax.php'),
            'nonce' => wp_create_nonce('upsell_add_to_cart_nonce'),
            'wc_ajax_url' => WC_AJAX::get_endpoint('%%endpoint%%'),
        ));
    }
}

// AJAX handler per aggiungere prodotti al carrello
add_action('wp_ajax_upsell_add_to_cart', 'upsell_ajax_add_to_cart');
add_action('wp_ajax_nopriv_upsell_add_to_cart', 'upsell_ajax_add_to_cart');

function upsell_ajax_add_to_cart() {
    // Verifica nonce per sicurezza
    if (!wp_verify_nonce($_POST['nonce'], 'upsell_add_to_cart_nonce')) {
        wp_die('Errore di sicurezza');
    }

    $product_id = intval($_POST['product_id']);
    $quantity = isset($_POST['quantity']) ? intval($_POST['quantity']) : 1;

    // Aggiungi il prodotto al carrello
    $result = WC()->cart->add_to_cart($product_id, $quantity);

    if ($result) {
        // Successo - restituisci i frammenti del carrello aggiornati
        WC_AJAX::get_refreshed_fragments();
    } else {
        wp_send_json_error(array(
            'message' => 'Errore durante l\'aggiunta del prodotto al carrello'
        ));
    }
}

(globalThis.webpackChunkwebpackWcBlocksFrontendJsonp=globalThis.webpackChunkwebpackWcBlocksFrontendJsonp||[]).push([[388],{4023:(e,r,c)=>{"use strict";c.r(r),c.d(r,{Block:()=>g,default:()=>x});var t=c(4921),s=c(7723),o=c(4656),a=c(910),n=c(6087),l=(c(8501),c(790));const i=({currency:e,maxPrice:r,minPrice:c,priceClassName:n,priceStyle:i={}})=>(0,l.jsxs)(l.Fragment,{children:[(0,l.jsx)("span",{className:"screen-reader-text",children:(0,s.sprintf)(/* translators: %1$s min price, %2$s max price */ /* translators: %1$s min price, %2$s max price */
(0,s.__)("Price between %1$s and %2$s","woocommerce"),(0,a.formatPrice)(c),(0,a.formatPrice)(r))}),(0,l.jsxs)("span",{"aria-hidden":!0,children:[(0,l.jsx)(o.FormattedMonetaryAmount,{className:(0,t.A)("wc-block-components-product-price__value",n),currency:e,value:c,style:i})," — ",(0,l.jsx)(o.FormattedMonetaryAmount,{className:(0,t.A)("wc-block-components-product-price__value",n),currency:e,value:r,style:i})]})]}),u=({currency:e,regularPriceClassName:r,regularPriceStyle:c,regularPrice:a,priceClassName:n,priceStyle:i,price:u})=>(0,l.jsxs)(l.Fragment,{children:[(0,l.jsx)("span",{className:"screen-reader-text",children:(0,s.__)("Previous price:","woocommerce")}),(0,l.jsx)(o.FormattedMonetaryAmount,{currency:e,renderText:e=>(0,l.jsx)("del",{className:(0,t.A)("wc-block-components-product-price__regular",r),style:c,children:e}),value:a}),(0,l.jsx)("span",{className:"screen-reader-text",children:(0,s.__)("Discounted price:","woocommerce")}),(0,l.jsx)(o.FormattedMonetaryAmount,{currency:e,renderText:e=>(0,l.jsx)("ins",{className:(0,t.A)("wc-block-components-product-price__value","is-discounted",n),style:i,children:e}),value:u})]}),p=({align:e,className:r,currency:c,format:s="<price/>",maxPrice:a,minPrice:p,price:m,priceClassName:d,priceStyle:y,regularPrice:g,regularPriceClassName:x,regularPriceStyle:N,style:_})=>{const f=(0,t.A)(r,"price","wc-block-components-product-price",{[`wc-block-components-product-price--align-${e}`]:e});s.includes("<price/>")||(s="<price/>",console.error("Price formats need to include the `<price/>` tag."));const h=g&&m&&m<g;let b=(0,l.jsx)("span",{className:(0,t.A)("wc-block-components-product-price__value",d)});return h?b=(0,l.jsx)(u,{currency:c,price:m,priceClassName:d,priceStyle:y,regularPrice:g,regularPriceClassName:x,regularPriceStyle:N}):void 0!==p&&void 0!==a?b=(0,l.jsx)(i,{currency:c,maxPrice:a,minPrice:p,priceClassName:d,priceStyle:y}):m&&(b=(0,l.jsx)(o.FormattedMonetaryAmount,{className:(0,t.A)("wc-block-components-product-price__value",d),currency:c,value:m,style:y})),(0,l.jsx)("span",{className:f,style:_,children:(0,n.createInterpolateElement)(s,{price:b})})};var m=c(2796),d=c(41),y=c(1616);const g=e=>{const{className:r,textAlign:c,isDescendentOfSingleProductTemplate:s}=e,o=(0,d.p)(e),{parentName:n,parentClassName:i}=(0,m.useInnerBlockLayoutContext)(),{product:u}=(0,m.useProductDataContext)(),y="woocommerce/all-products"===n,g=s&&!("woocommerce/add-to-cart-with-options-grouped-product-item"===n),x=(0,t.A)("wc-block-components-product-price",r,o.className,{[`${i}__product-price`]:i});if(!u.id&&!s){const e=(0,l.jsx)(p,{align:c,className:x});return y?(0,l.jsx)("div",{className:"wp-block-woocommerce-product-price",children:e}):e}const N=u.prices,_=g?(0,a.getCurrencyFromPriceResponse)():(0,a.getCurrencyFromPriceResponse)(N),f="5000",h=N.price!==N.regular_price,b=(0,t.A)({[`${i}__product-price__value`]:i,[`${i}__product-price__value--on-sale`]:h}),P=(0,l.jsx)(p,{align:c,className:x,style:o.style,regularPriceStyle:o.style,priceStyle:o.style,priceClassName:b,currency:_,price:g?f:N.price,minPrice:N?.price_range?.min_amount,maxPrice:N?.price_range?.max_amount,regularPrice:g?f:N.regular_price,regularPriceClassName:(0,t.A)({[`${i}__product-price__regular`]:i})});return y?(0,l.jsx)("div",{className:"wp-block-woocommerce-product-price",children:P}):P},x=e=>e.isDescendentOfSingleProductTemplate?(0,l.jsx)(g,{...e}):(0,y.withProductDataContext)(g)(e)},41:(e,r,c)=>{"use strict";c.d(r,{p:()=>i});var t=c(4921),s=c(3993),o=c(7356),a=c(9786);function n(e={}){const r={};return(0,a.getCSSRules)(e,{selector:""}).forEach((e=>{r[e.key]=e.value})),r}function l(e,r){return e&&r?`has-${(0,o.c)(r)}-${e}`:""}const i=e=>{const r=(e=>{const r=(0,s.isObject)(e)?e:{style:{}};let c=r.style;return(0,s.isString)(c)&&(c=JSON.parse(c)||{}),(0,s.isObject)(c)||(c={}),{...r,style:c}})(e),c=function(e){const{backgroundColor:r,textColor:c,gradient:o,style:a}=e,i=l("background-color",r),u=l("color",c),p=function(e){if(e)return`has-${e}-gradient-background`}(o),m=p||a?.color?.gradient;return{className:(0,t.A)(u,p,{[i]:!m&&!!i,"has-text-color":c||a?.color?.text,"has-background":r||a?.color?.background||o||a?.color?.gradient,"has-link-color":(0,s.isObject)(a?.elements?.link)?a?.elements?.link?.color:void 0}),style:n({color:a?.color||{}})}}(r),o=function(e){const r=e.style?.border||{};return{className:function(e){const{borderColor:r,style:c}=e,s=r?l("border-color",r):"";return(0,t.A)({"has-border-color":!!r||!!c?.border?.color,[s]:!!s})}(e),style:n({border:r})}}(r),a=function(e){return{className:void 0,style:n({spacing:e.style?.spacing||{}})}}(r),i=(e=>{const r=(0,s.isObject)(e.style.typography)?e.style.typography:{},c=(0,s.isString)(r.fontFamily)?r.fontFamily:"";return{className:e.fontFamily?`has-${e.fontFamily}-font-family`:c,style:{fontSize:e.fontSize?`var(--wp--preset--font-size--${e.fontSize})`:r.fontSize,fontStyle:r.fontStyle,fontWeight:r.fontWeight,letterSpacing:r.letterSpacing,lineHeight:r.lineHeight,textDecoration:r.textDecoration,textTransform:r.textTransform}}})(r);return{className:(0,t.A)(i.className,c.className,o.className,a.className),style:{...i.style,...c.style,...o.style,...a.style}}}},8501:()=>{}}]);
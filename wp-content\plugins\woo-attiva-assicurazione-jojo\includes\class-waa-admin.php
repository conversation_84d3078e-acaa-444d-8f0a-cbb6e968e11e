<?php
/**
 * Classe per la gestione dell'interfaccia di amministrazione delle assicurazioni
 *
 * @package Woo_Attiva_Assicurazione
 */

// Prevengo l'accesso diretto al file
if (!defined('ABSPATH')) {
    exit;
}

/**
 * Classe per la gestione dell'interfaccia di amministrazione delle assicurazioni
 */
class WAA_Admin {

    /**
     * Inizializza la classe
     */
    public static function init() {
        // Metabox per la pagina dell'ordine
        add_action('add_meta_boxes', array(__CLASS__, 'add_order_meta_box'));
        
        // Gestione dei form POST
        add_action('admin_init', array(__CLASS__, 'handle_insurance_forms'));
        
        // Aggiungo il submenu Assicurazioni
        add_action('admin_menu', array(__CLASS__, 'add_insurance_menu'));
        
        // Mostro i dati dell'assicurazione nei dettagli dell'ordine
        add_action('woocommerce_admin_order_data_after_order_details', array(__CLASS__, 'display_insurance_data_in_order'), 10, 1);
    }

    /**
     * Aggiunge il metabox nella pagina dell'ordine
     */
    public static function add_order_meta_box() {
        add_meta_box(
            'waa-insurance-box',
            __('Gestione Assicurazioni', 'woo-attiva-assicurazione'),
            array(__CLASS__, 'render_order_meta_box'),
            'shop_order',
            'normal',
            'high'
        );
    }

    /**
     * Renderizza il contenuto del metabox dell'ordine
     *
     * @param WP_Post $post Oggetto post dell'ordine
     */
    public static function render_order_meta_box($post) {
        $order_id = $post->ID;
        $order = wc_get_order($order_id);
        
        if (!$order) {
            echo '<p>' . esc_html__('Ordine non trovato.', 'woo-attiva-assicurazione') . '</p>';
            return;
        }
        
        $has_insurance_products = false;
        $insurance_products = array();
        $all_products = array();
        
        // Ottengo tutti i prodotti dell'ordine
        foreach ($order->get_items() as $item) {
            $product_id = $item->get_product_id();
            $product = wc_get_product($product_id);
            
            if (!$product) {
                continue;
            }
            
            // Aggiungo il prodotto all'elenco di tutti i prodotti
            $all_products[] = array(
                'id' => $product_id,
                'name' => $item->get_name(),
                'item_id' => $item->get_id()
            );
            
            // Verifico se il prodotto appartiene alla categoria "Assicurazioni" per mostrare il metabox
            $categories = wp_get_post_terms($product_id, 'product_cat');
            
            foreach ($categories as $category) {
                if (strtolower($category->name) === 'assicurazioni') {
                    $has_insurance_products = true;
                    $insurance_products[] = array(
                        'id' => $product_id,
                        'name' => $item->get_name(),
                        'item_id' => $item->get_id()
                    );
                    break;
                }
            }
        }
        
        // Mostro il metabox solo se ci sono prodotti nella categoria assicurazioni
        if (!$has_insurance_products) {
            echo '<p>' . esc_html__('Nessun prodotto di tipo assicurazione presente in questo ordine.', 'woo-attiva-assicurazione') . '</p>';
            return;
        }
        
        // Ottengo le assicurazioni già registrate per questo ordine
        $insurances = WAA_DB::get_insurances_by_order($order_id);
        $registered_products = array();
        
        foreach ($insurances as $insurance) {
            $registered_products[$insurance['product_name']] = $insurance;
        }
        
        // Informazioni cliente
        $customer_name = $order->get_billing_first_name() . ' ' . $order->get_billing_last_name();
        $customer_email = $order->get_billing_email();
        
        // Output del metabox
        echo '<div class="waa-insurance-container">';
        
        // Mostro le assicurazioni già registrate
        if (!empty($insurances)) {
            echo '<h3>' . esc_html__('Assicurazioni registrate', 'woo-attiva-assicurazione') . '</h3>';
            echo '<table class="waa-insurance-table widefat">';
            echo '<thead><tr>';
            echo '<th>' . esc_html__('Prodotto', 'woo-attiva-assicurazione') . '</th>';
            echo '<th>' . esc_html__('IMEI', 'woo-attiva-assicurazione') . '</th>';
            echo '<th>' . esc_html__('Data Attivazione', 'woo-attiva-assicurazione') . '</th>';
            echo '<th>' . esc_html__('Stato', 'woo-attiva-assicurazione') . '</th>';
            echo '<th>' . esc_html__('Azioni', 'woo-attiva-assicurazione') . '</th>';
            echo '</tr></thead><tbody>';
            
            foreach ($insurances as $insurance) {
                echo '<tr>';
                echo '<td>' . esc_html($insurance['product_name']) . '</td>';
                echo '<td>' . esc_html($insurance['imei']) . '</td>';
                echo '<td>' . date_i18n(get_option('date_format'), strtotime($insurance['activation_date'])) . '</td>';
                echo '<td>' . esc_html(ucfirst($insurance['status'])) . '</td>';
                echo '<td>';
                
                if ($insurance['status'] === 'registrata') {
                    // Form per utilizzare l'assicurazione
                    echo '<form class="waa-use-insurance-form" method="post" action="">';
                    echo '<input type="hidden" name="waa_use_insurance" value="1">';
                    echo '<input type="hidden" name="waa_admin_nonce" value="' . wp_create_nonce('waa_admin_nonce') . '">';
                    echo '<input type="hidden" name="insurance_id" value="' . esc_attr($insurance['id']) . '">';
                    echo '<input type="hidden" name="order_id" value="' . esc_attr($order_id) . '">';
                    echo '<div class="waa-form-row">';
                    echo '<label for="usage_date_' . esc_attr($insurance['id']) . '">' . esc_html__('Data Utilizzo:', 'woo-attiva-assicurazione') . '</label>';
                    echo '<input type="date" name="usage_date" id="usage_date_' . esc_attr($insurance['id']) . '" required>';
                    echo '</div>';
                    echo '<div class="waa-form-row">';
                    echo '<label for="usage_reason_' . esc_attr($insurance['id']) . '">' . esc_html__('Motivazione:', 'woo-attiva-assicurazione') . '</label>';
                    echo '<textarea name="usage_reason" id="usage_reason_' . esc_attr($insurance['id']) . '" required></textarea>';
                    echo '</div>';
                    echo '<div class="waa-form-row">';
                    echo '<button type="submit" class="button action">' . esc_html__('Utilizza Assicurazione', 'woo-attiva-assicurazione') . '</button>';
                    echo '</div>';
                    echo '</form>';
                } elseif ($insurance['status'] === 'utilizzata') {
                    // Informazioni sull'utilizzo
                    echo '<div class="waa-insurance-used-info">';
                    echo '<p><strong>' . esc_html__('Data Utilizzo:', 'woo-attiva-assicurazione') . '</strong> ';
                    echo date_i18n(get_option('date_format'), strtotime($insurance['usage_date'])) . '</p>';
                    echo '<p><strong>' . esc_html__('Motivazione:', 'woo-attiva-assicurazione') . '</strong> ';
                    echo esc_html($insurance['usage_reason']) . '</p>';
                    echo '</div>';
                }
                
                echo '</td>';
                echo '</tr>';
            }
            
            echo '</tbody></table>';
        }
        
        // Form per registrare nuove assicurazioni
        $unregistered_products = array();
        foreach ($all_products as $product) {
            if (!isset($registered_products[$product['name']])) {
                $unregistered_products[] = $product;
            }
        }
        
        if (!empty($unregistered_products)) {
            echo '<h3>' . esc_html__('Registra nuova assicurazione', 'woo-attiva-assicurazione') . '</h3>';
            echo '<form id="waa-register-insurance-form" method="post" action="">';
            echo '<input type="hidden" name="waa_register_insurance" value="1">';
            echo '<input type="hidden" name="waa_admin_nonce" value="' . wp_create_nonce('waa_admin_nonce') . '">';
            echo '<input type="hidden" name="order_id" value="' . esc_attr($order_id) . '">';
            echo '<input type="hidden" name="customer_name" value="' . esc_attr($customer_name) . '">';
            echo '<input type="hidden" name="customer_email" value="' . esc_attr($customer_email) . '">';
            
            echo '<div class="waa-form-row">';
            echo '<label for="product_name">' . esc_html__('Prodotto:', 'woo-attiva-assicurazione') . '</label>';
            echo '<select name="product_name" id="product_name" required>';
            echo '<option value="">' . esc_html__('-- Seleziona Prodotto --', 'woo-attiva-assicurazione') . '</option>';
            
            foreach ($unregistered_products as $product) {
                echo '<option value="' . esc_attr($product['name']) . '">' . esc_html($product['name']) . '</option>';
            }
            
            echo '</select>';
            echo '</div>';
            
            echo '<div class="waa-form-row">';
            echo '<label for="imei">' . esc_html__('IMEI:', 'woo-attiva-assicurazione') . '</label>';
            echo '<input type="text" name="imei" id="imei" required>';
            echo '</div>';
            
            echo '<div class="waa-form-row">';
            echo '<label for="activation_date">' . esc_html__('Data Attivazione:', 'woo-attiva-assicurazione') . '</label>';
            echo '<input type="date" name="activation_date" id="activation_date" required>';
            echo '</div>';
            
            echo '<div class="waa-form-row">';
            echo '<button type="submit" class="button button-primary">' . esc_html__('Registra Assicurazione', 'woo-attiva-assicurazione') . '</button>';
            echo '</div>';
            
            echo '</form>';
        }
        
        // Aggiungo messaggi di stato
        echo '<div id="waa-message" class="waa-message" style="display: none;"></div>';
        
        echo '</div>';
        
        // Nonce per sicurezza
        wp_nonce_field('waa_admin_nonce', 'waa_admin_nonce');
    }

    /**
     * Gestisce i form di registrazione e utilizzo delle assicurazioni
     */
    public static function handle_insurance_forms() {
        // Verifica se è una richiesta POST e se c'è un'azione definita
        if (!isset($_SERVER['REQUEST_METHOD']) || $_SERVER['REQUEST_METHOD'] !== 'POST') {
            return;
        }
        
        // Debug
        error_log('WAA: Richiesta POST ricevuta nel metodo handle_insurance_forms');
        error_log('WAA: POST data: ' . print_r($_POST, true));
        
        // Gestione del form di registrazione assicurazione
        if (isset($_POST['waa_register_insurance']) && isset($_POST['waa_admin_nonce'])) {
            // Debug
            error_log('WAA: Form di registrazione assicurazione ricevuto');
            
            // Verifica nonce
            if (!wp_verify_nonce($_POST['waa_admin_nonce'], 'waa_admin_nonce')) {
                error_log('WAA: Errore di verifica nonce');
                wp_die(__('Errore di sicurezza. Riprova.', 'woo-attiva-assicurazione'));
            }
            
            // Verifica autorizzazioni
            if (!current_user_can('edit_shop_orders')) {
                error_log('WAA: Utente non autorizzato');
                wp_die(__('Non hai i permessi per eseguire questa azione.', 'woo-attiva-assicurazione'));
            }
            
            $order_id = isset($_POST['order_id']) ? intval($_POST['order_id']) : 0;
            $product_name = isset($_POST['product_name']) ? sanitize_text_field($_POST['product_name']) : '';
            $imei = isset($_POST['imei']) ? sanitize_text_field($_POST['imei']) : '';
            $activation_date = isset($_POST['activation_date']) ? sanitize_text_field($_POST['activation_date']) : '';
            $customer_name = isset($_POST['customer_name']) ? sanitize_text_field($_POST['customer_name']) : '';
            $customer_email = isset($_POST['customer_email']) ? sanitize_email($_POST['customer_email']) : '';
            
            // Debug
            error_log("WAA: Dati form - order_id: $order_id, product: $product_name, imei: $imei, activation: $activation_date");
            
            // Validazione campi obbligatori
            if (empty($order_id) || empty($product_name) || empty($imei) || empty($activation_date) || empty($customer_name) || empty($customer_email)) {
                error_log('WAA: Campi obbligatori mancanti');
                wp_redirect(add_query_arg(array('message' => 'missing-fields'), admin_url('post.php?post=' . $order_id . '&action=edit')));
                exit;
            }
            
            // Controlla se esiste già un'assicurazione per questo prodotto
            $existing = WAA_DB::get_insurance_by_product($order_id, $product_name);
            if ($existing) {
                error_log('WAA: Assicurazione già esistente per questo prodotto');
                wp_redirect(add_query_arg(array('message' => 'duplicate-insurance'), admin_url('post.php?post=' . $order_id . '&action=edit')));
                exit;
            }
            
            // Inserisci l'assicurazione nel database
            $data = array(
                'order_id' => $order_id,
                'customer_name' => $customer_name,
                'customer_email' => $customer_email,
                'product_name' => $product_name,
                'imei' => $imei,
                'activation_date' => $activation_date
            );
            
            error_log('WAA: Tentativo di inserimento assicurazione nel database');
            $insurance_id = WAA_DB::insert_insurance($data);
            
            if (!$insurance_id) {
                error_log('WAA: Errore nell\'inserimento dell\'assicurazione');
                wp_redirect(add_query_arg(array('message' => 'insert-error'), admin_url('post.php?post=' . $order_id . '&action=edit')));
                exit;
            }
            
            error_log("WAA: Assicurazione inserita con successo. ID: $insurance_id");
            
            // Aggiorna l'ordine
            $order = wc_get_order($order_id);
            
            // Aggiungi una nota all'ordine
            $note = sprintf(
                __('Assicurazione registrata per il prodotto "%s" con IMEI: %s e data di attivazione: %s', 'woo-attiva-assicurazione'),
                $product_name,
                $imei,
                $activation_date
            );
            
            $order->add_order_note($note);
            
            // Aggiungi meta all'ordine
            update_post_meta($order_id, '_has_waa_insurance', 'yes');
            update_post_meta($order_id, '_waa_insurance_product', $product_name);
            update_post_meta($order_id, '_waa_insurance_imei', $imei);
            update_post_meta($order_id, '_waa_insurance_activation_date', $activation_date);
            
            // Salva l'ordine
            $order->save();
            
            // Invia email di notifica
            $email_sent = WAA_Email::send_registration_email($order, $data);
            
            // Log dell'operazione
            error_log(sprintf('WAA: Assicurazione registrata per l\'ordine #%s, prodotto %s, IMEI %s', $order_id, $product_name, $imei));
            
            // Redirect con messaggio di successo
            $message_param = $email_sent ? 'success' : 'email-error';
            wp_redirect(add_query_arg(array('message' => $message_param), admin_url('post.php?post=' . $order_id . '&action=edit')));
            exit;
        }
        
        // Gestione del form di utilizzo assicurazione
        if (isset($_POST['waa_use_insurance']) && isset($_POST['waa_admin_nonce'])) {
            // Verifica nonce
            if (!wp_verify_nonce($_POST['waa_admin_nonce'], 'waa_admin_nonce')) {
                wp_die(__('Errore di sicurezza. Riprova.', 'woo-attiva-assicurazione'));
            }
            
            // Verifica autorizzazioni
            if (!current_user_can('edit_shop_orders')) {
                wp_die(__('Non hai i permessi per eseguire questa azione.', 'woo-attiva-assicurazione'));
            }
            
            $insurance_id = isset($_POST['insurance_id']) ? intval($_POST['insurance_id']) : 0;
            $order_id = isset($_POST['order_id']) ? intval($_POST['order_id']) : 0;
            $usage_reason = isset($_POST['usage_reason']) ? sanitize_textarea_field($_POST['usage_reason']) : '';
            $usage_date = isset($_POST['usage_date']) ? sanitize_text_field($_POST['usage_date']) : '';
            
            // Validazione
            if (empty($insurance_id) || empty($order_id) || empty($usage_reason) || empty($usage_date)) {
                wp_redirect(add_query_arg(array('message' => 'missing-fields'), admin_url('post.php?post=' . $order_id . '&action=edit')));
                exit;
            }
            
            // Verifica assicurazione
            $insurances = WAA_DB::get_insurances_by_order($order_id);
            $insurance = null;
            
            foreach ($insurances as $ins) {
                if ($ins['id'] == $insurance_id) {
                    $insurance = $ins;
                    break;
                }
            }
            
            if (!$insurance) {
                wp_redirect(add_query_arg(array('message' => 'insurance-not-found'), admin_url('post.php?post=' . $order_id . '&action=edit')));
                exit;
            }
            
            if ($insurance['status'] !== 'registrata') {
                wp_redirect(add_query_arg(array('message' => 'insurance-already-used'), admin_url('post.php?post=' . $order_id . '&action=edit')));
                exit;
            }
            
            // Aggiorna lo stato dell'assicurazione
            $updated = WAA_DB::mark_insurance_as_used($insurance_id, $usage_reason, $usage_date);
            
            if (!$updated) {
                wp_redirect(add_query_arg(array('message' => 'update-error'), admin_url('post.php?post=' . $order_id . '&action=edit')));
                exit;
            }
            
            // Aggiorna l'ordine
            $order = wc_get_order($order_id);
            
            // Aggiungi nota all'ordine
            $note = sprintf(
                __('Assicurazione utilizzata per il prodotto "%s" con motivazione: %s', 'woo-attiva-assicurazione'),
                $insurance['product_name'],
                $usage_reason
            );
            
            $order->add_order_note($note);
            
            // Aggiungi meta all'ordine
            update_post_meta($order_id, '_waa_insurance_used', 'yes');
            update_post_meta($order_id, '_waa_insurance_usage_date', $usage_date);
            update_post_meta($order_id, '_waa_insurance_usage_reason', $usage_reason);
            
            // Salva l'ordine
            $order->save();
            
            // Invia email
            $insurance['usage_reason'] = $usage_reason;
            $insurance['usage_date'] = $usage_date;
            $email_sent = WAA_Email::send_usage_email($order, $insurance);
            
            // Log dell'operazione
            error_log(sprintf('WAA: Assicurazione utilizzata per l\'ordine #%s, prodotto %s, motivazione: %s', $order_id, $insurance['product_name'], $usage_reason));
            
            // Redirect con messaggio
            $message_param = $email_sent ? 'use-success' : 'use-email-error';
            wp_redirect(add_query_arg(array('message' => $message_param), admin_url('post.php?post=' . $order_id . '&action=edit')));
            exit;
        }
    }

    /**
     * Aggiunge il submenu Assicurazioni sotto WooCommerce
     */
    public static function add_insurance_menu() {
        add_submenu_page(
            'woocommerce',
            __('Assicurazioni', 'woo-attiva-assicurazione'),
            __('Assicurazioni', 'woo-attiva-assicurazione'),
            'manage_woocommerce',
            'waa-insurances',
            array(__CLASS__, 'render_insurances_page')
        );
    }

    /**
     * Renderizza la pagina che elenca tutte le assicurazioni
     */
    public static function render_insurances_page() {
        global $wpdb;
        
        $table_name = $wpdb->prefix . WAA_DB::get_table_name();
        
        // Recupero tutte le assicurazioni
        $insurances = $wpdb->get_results(
            "SELECT * FROM $table_name ORDER BY id DESC",
            ARRAY_A
        );
        
        ?>
        <div class="wrap">
            <h1><?php echo esc_html__('Assicurazioni Registrate', 'woo-attiva-assicurazione'); ?></h1>
            
            <div class="waa-insurance-container">
                <?php if (!empty($insurances)) : ?>
                    <table class="waa-insurance-table widefat">
                        <thead>
                            <tr>
                                <th><?php echo esc_html__('ID', 'woo-attiva-assicurazione'); ?></th>
                                <th><?php echo esc_html__('Ordine', 'woo-attiva-assicurazione'); ?></th>
                                <th><?php echo esc_html__('Cliente', 'woo-attiva-assicurazione'); ?></th>
                                <th><?php echo esc_html__('Email', 'woo-attiva-assicurazione'); ?></th>
                                <th><?php echo esc_html__('Prodotto', 'woo-attiva-assicurazione'); ?></th>
                                <th><?php echo esc_html__('IMEI', 'woo-attiva-assicurazione'); ?></th>
                                <th><?php echo esc_html__('Data Attivazione', 'woo-attiva-assicurazione'); ?></th>
                                <th><?php echo esc_html__('Stato', 'woo-attiva-assicurazione'); ?></th>
                                <th><?php echo esc_html__('Azioni', 'woo-attiva-assicurazione'); ?></th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($insurances as $insurance) : ?>
                                <tr>
                                    <td><?php echo esc_html($insurance['id']); ?></td>
                                    <td>
                                        <a href="<?php echo esc_url(admin_url('post.php?post=' . $insurance['order_id'] . '&action=edit')); ?>">
                                            #<?php echo esc_html($insurance['order_id']); ?>
                                        </a>
                                    </td>
                                    <td><?php echo esc_html($insurance['customer_name']); ?></td>
                                    <td><?php echo esc_html($insurance['customer_email']); ?></td>
                                    <td><?php echo esc_html($insurance['product_name']); ?></td>
                                    <td><?php echo esc_html($insurance['imei']); ?></td>
                                    <td><?php echo date_i18n(get_option('date_format'), strtotime($insurance['activation_date'])); ?></td>
                                    <td><?php echo esc_html(ucfirst($insurance['status'])); ?></td>
                                    <td>
                                        <a href="<?php echo esc_url(admin_url('post.php?post=' . $insurance['order_id'] . '&action=edit')); ?>" class="button">
                                            <?php echo esc_html__('Visualizza Ordine', 'woo-attiva-assicurazione'); ?>
                                        </a>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                <?php else : ?>
                    <p><?php echo esc_html__('Nessuna assicurazione registrata.', 'woo-attiva-assicurazione'); ?></p>
                <?php endif; ?>
            </div>
        </div>
        <?php
    }

    /**
     * Mostra i dati dell'assicurazione nella pagina dell'ordine
     *
     * @param WC_Order $order L'oggetto ordine
     */
    public static function display_insurance_data_in_order($order) {
        $order_id = $order->get_id();
        $has_insurance = get_post_meta($order_id, '_has_waa_insurance', true);
        
        if ($has_insurance !== 'yes') {
            return;
        }
        
        $product = get_post_meta($order_id, '_waa_insurance_product', true);
        $imei = get_post_meta($order_id, '_waa_insurance_imei', true);
        $activation_date = get_post_meta($order_id, '_waa_insurance_activation_date', true);
        $used = get_post_meta($order_id, '_waa_insurance_used', true);
        $usage_date = get_post_meta($order_id, '_waa_insurance_usage_date', true);
        $usage_reason = get_post_meta($order_id, '_waa_insurance_usage_reason', true);
        
        ?>
        <div class="order_data_column">
            <h4><?php _e('Dati Assicurazione', 'woo-attiva-assicurazione'); ?></h4>
            <div class="address">
                <p>
                    <strong><?php _e('Prodotto:', 'woo-attiva-assicurazione'); ?></strong>
                    <?php echo esc_html($product); ?>
                </p>
                <p>
                    <strong><?php _e('IMEI:', 'woo-attiva-assicurazione'); ?></strong>
                    <?php echo esc_html($imei); ?>
                </p>
                <p>
                    <strong><?php _e('Data Attivazione:', 'woo-attiva-assicurazione'); ?></strong>
                    <?php echo date_i18n(get_option('date_format'), strtotime($activation_date)); ?>
                </p>
                <p>
                    <strong><?php _e('Stato:', 'woo-attiva-assicurazione'); ?></strong>
                    <?php echo $used === 'yes' ? esc_html__('Utilizzata', 'woo-attiva-assicurazione') : esc_html__('Registrata', 'woo-attiva-assicurazione'); ?>
                </p>
                
                <?php if ($used === 'yes' && !empty($usage_date) && !empty($usage_reason)) : ?>
                <p>
                    <strong><?php _e('Data Utilizzo:', 'woo-attiva-assicurazione'); ?></strong>
                    <?php echo date_i18n(get_option('date_format'), strtotime($usage_date)); ?>
                </p>
                <p>
                    <strong><?php _e('Motivazione Utilizzo:', 'woo-attiva-assicurazione'); ?></strong>
                    <?php echo esc_html($usage_reason); ?>
                </p>
                <?php endif; ?>
            </div>
        </div>
        <?php
    }
}

// Inizializza la classe
WAA_Admin::init(); 
# Woo Product Addons Jojo

Un plugin per WooCommerce che permette di selezionare prodotti accessori da aggiungere al carrello insieme al prodotto principale.

## Descrizione

Woo Product Addons Jojo è un plugin che consente agli amministratori di un negozio WooCommerce di selezionare prodotti esistenti come accessori per altri prodotti. Questi accessori vengono visualizzati nella pagina del prodotto principale e possono essere aggiunti al carrello insieme ad esso.

### Caratteristiche principali

- **Interfaccia amministrativa intuitiva**: Ricerca AJAX per selezionare facilmente i prodotti accessori
- **Visualizzazione elegante**: Gli accessori vengono mostrati in una lista ordinata con immagine, nome e prezzo
- **Esperienza utente migliorata**: Gli utenti possono selezionare quali accessori aggiungere al carrello
- **Completamente responsive**: Funziona perfettamente su dispositivi mobili e desktop
- **Personalizzabile**: Stili CSS facilmente modificabili per adattarsi al tema del sito

## Installazione

1. Carica la cartella `woo-product-addons-jojo` nella directory `/wp-content/plugins/` del tuo sito WordPress
2. Attiva il plugin dalla sezione 'Plugin' in WordPress
3. Assicurati che WooCommerce sia installato e attivato

## Utilizzo

### Per gli amministratori

1. Vai alla pagina di modifica di un prodotto in WooCommerce
2. Nella sezione "Dati prodotto", troverai un campo chiamato "Prodotti Accessori"
3. Inizia a digitare il nome di un prodotto per cercarlo
4. Seleziona i prodotti che desideri offrire come accessori
5. Salva il prodotto

### Per gli utenti

1. Visita la pagina di un prodotto che ha accessori configurati
2. Sotto la descrizione del prodotto, vedrai una lista di accessori consigliati
3. Seleziona gli accessori che desideri acquistare insieme al prodotto principale
4. Clicca su "Aggiungi al carrello"
5. Tutti i prodotti selezionati verranno aggiunti al carrello

## Requisiti

- WordPress 5.6 o superiore
- WooCommerce 5.0 o superiore
- PHP 7.2 o superiore

## Personalizzazione

Il plugin include file CSS separati per la parte amministrativa e frontend, che possono essere facilmente modificati per adattarsi al design del tuo sito:

- `assets/css/admin.css`: Stili per l'interfaccia amministrativa
- `assets/css/frontend.css`: Stili per la visualizzazione degli accessori nella pagina del prodotto

## Supporto

Per domande, problemi o suggerimenti, contattare l'autore del plugin.

## Licenza

Questo plugin è rilasciato sotto la licenza GPL v2 o successiva.

## Changelog

### 1.0.0
- Versione iniziale del plugin 
/**
 * Admin styles for Report Assicurazioni plugin
 */

.report-filters {
    background: #fff;
    border: 1px solid #ccd0d4;
    border-radius: 4px;
    padding: 20px;
    margin: 20px 0;
}

.report-filters .form-table {
    margin-bottom: 0;
}

.report-filters .form-table th {
    width: 150px;
    padding-left: 0;
}

.report-filters .form-table td {
    padding-left: 20px;
}

.report-filters input[type="date"],
.report-filters input[type="text"] {
    width: 250px;
}

.report-actions {
    margin: 20px 0;
    padding: 15px 0;
    border-bottom: 1px solid #ccd0d4;
}

.report-actions .button {
    margin-right: 10px;
}

.report-table-container {
    background: #fff;
    border: 1px solid #ccd0d4;
    border-radius: 4px;
    overflow: hidden;
}

.report-table-container .wp-list-table {
    border: none;
    margin: 0;
}

.report-table-container .wp-list-table th,
.report-table-container .wp-list-table td {
    padding: 12px 15px;
}

.report-table-container .wp-list-table th {
    background: #f8f9fa;
    border-bottom: 2px solid #dee2e6;
    font-weight: 600;
}

.report-table-container .wp-list-table td {
    border-bottom: 1px solid #dee2e6;
    vertical-align: top;
}

.report-table-container .wp-list-table tbody tr:hover {
    background: #f8f9fa;
}

.report-table-container .wp-list-table tbody tr:hover td {
    background: transparent;
}

/* Order number column */
.report-table-container .wp-list-table td:first-child {
    font-weight: 600;
}

.report-table-container .wp-list-table td:first-child a {
    color: #0073aa;
    text-decoration: none;
}

.report-table-container .wp-list-table td:first-child a:hover {
    color: #005177;
    text-decoration: underline;
}

/* Products column */
.report-table-container .wp-list-table td:nth-child(3) {
    max-width: 300px;
    word-wrap: break-word;
    line-height: 1.4;
}

/* Total amount column */
.report-table-container .wp-list-table td:last-child {
    text-align: right;
    font-weight: 600;
    color: #2c5aa0;
}

/* Loading state */
.report-loading {
    text-align: center;
    padding: 40px;
    color: #666;
}

.report-loading .spinner {
    float: none;
    margin: 0 auto 10px;
    display: block;
}

/* Empty state styling */
.report-empty-state {
    text-align: center;
    padding: 40px 20px;
    color: #666;
}

.report-empty-state p {
    margin: 10px 0;
    font-size: 14px;
}

.report-empty-state p:first-child {
    font-size: 16px;
    color: #333;
}

.no-filters-message,
.no-results-message {
    background: #f9f9f9;
    border-left: 4px solid #0073aa;
}

.no-results-message {
    border-left-color: #d63638;
}

/* Export button states */
#export-pdf.loading {
    opacity: 0.6;
    pointer-events: none;
}

#export-pdf.loading:before {
    content: "";
    display: inline-block;
    width: 16px;
    height: 16px;
    margin-right: 8px;
    border: 2px solid #ccc;
    border-top-color: #333;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

#export-pdf:disabled {
    opacity: 0.5;
    cursor: not-allowed;
    pointer-events: none;
}

.report-actions .description {
    font-style: italic;
    color: #666;
}

@keyframes spin {
    to {
        transform: rotate(360deg);
    }
}

/* Responsive design */
@media screen and (max-width: 782px) {
    .report-filters .form-table th,
    .report-filters .form-table td {
        display: block;
        width: 100%;
        padding: 10px 0;
    }
    
    .report-filters .form-table th {
        border-bottom: none;
    }
    
    .report-filters input[type="date"],
    .report-filters input[type="text"] {
        width: 100%;
        max-width: 300px;
    }
    
    .report-table-container {
        overflow-x: auto;
    }
    
    .report-table-container .wp-list-table {
        min-width: 600px;
    }
}

/* Print styles for PDF export */
@media print {
    .report-filters,
    .report-actions,
    .wrap h1 {
        display: none;
    }
    
    .report-table-container {
        border: none;
        box-shadow: none;
    }
    
    .report-table-container .wp-list-table th,
    .report-table-container .wp-list-table td {
        border: 1px solid #000;
        padding: 8px;
    }
}

/* Error and success messages */
.report-message {
    margin: 15px 0;
    padding: 12px;
    border-radius: 4px;
}

.report-message.error {
    background: #fef2f2;
    border: 1px solid #fecaca;
    color: #991b1b;
}

.report-message.success {
    background: #f0fdf4;
    border: 1px solid #bbf7d0;
    color: #166534;
}

.report-message.info {
    background: #eff6ff;
    border: 1px solid #bfdbfe;
    color: #1e40af;
}

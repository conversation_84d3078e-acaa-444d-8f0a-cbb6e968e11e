(()=>{var e,t,r,s={4604:(e,t,r)=>{"use strict";const s=window.wp.blocks;var o=r(4530),i=r(3791),n=(r(4809),r(7723));const a=window.wp.blockEditor,c=window.wp.components;var l=r(4921),d=r(6087);function m(e,t,r){const s=new Set(t.map((e=>e[r])));return e.filter((e=>!s.has(e[r])))}const u=window.wp.htmlEntities;var h=r(790);const p={clear:(0,n.__)("Clear all selected items","woocommerce"),noItems:(0,n.__)("No items found.","woocommerce"),
/* Translators: %s search term */
noResults:(0,n.__)("No results for %s","woocommerce"),search:(0,n.__)("Search for items","woocommerce"),selected:e=>(0,n.sprintf)(/* translators: Number of items selected from list. */ /* translators: Number of items selected from list. */
(0,n._n)("%d item selected","%d items selected",e,"woocommerce"),e),updated:(0,n.__)("Search results updated.","woocommerce")},w=(e,t=e)=>{const r=e.reduce(((e,t)=>{const r=t.parent||0;return e[r]||(e[r]=[]),e[r].push(t),e}),{}),s=t.reduce(((e,t)=>(e[String(t.id)]=t,e)),{});const o=["0"],i=(e={})=>e.parent?[...i(s[e.parent]),e.name]:e.name?[e.name]:[],n=e=>e.map((e=>{const t=r[e.id];return o.push(""+e.id),{...e,breadcrumbs:i(s[e.parent]),children:t&&t.length?n(t):[]}})),a=n(r[0]||[]);return Object.entries(r).forEach((([e,t])=>{o.includes(e)||a.push(...n(t||[]))})),a},g=(e,t)=>{if(!t)return e;const r=new RegExp(`(${t.replace(/[-\/\\^$*+?.()|[\]{}]/g,"\\$&")})`,"ig");return e.split(r).map(((e,t)=>r.test(e)?(0,h.jsx)("strong",{children:e},t):(0,h.jsx)(d.Fragment,{children:e},t)))},v=({label:e})=>(0,h.jsx)("span",{className:"woocommerce-search-list__item-count",children:e}),_=e=>{const{item:t,search:r}=e,s=t.breadcrumbs&&t.breadcrumbs.length;return(0,h.jsxs)("span",{className:"woocommerce-search-list__item-label",children:[s?(0,h.jsx)("span",{className:"woocommerce-search-list__item-prefix",children:(o=t.breadcrumbs,1===o.length?o.slice(0,1).toString():2===o.length?o.slice(0,1).toString()+" › "+o.slice(-1).toString():o.slice(0,1).toString()+" … "+o.slice(-1).toString())}):null,(0,h.jsx)("span",{className:"woocommerce-search-list__item-name",children:g((0,u.decodeEntities)(t.name),r)})]});var o},b=({countLabel:e,className:t,depth:r=0,controlId:s="",item:o,isSelected:i,isSingle:n,onSelect:a,search:p="",selected:w,useExpandedPanelId:b,...x})=>{const[y,f]=b,j=null!=e&&void 0!==o.count&&null!==o.count,R=!!o.breadcrumbs?.length,k=!!o.children?.length,S=y===o.id,C=(0,l.A)(["woocommerce-search-list__item",`depth-${r}`,t],{"has-breadcrumbs":R,"has-children":k,"has-count":j,"is-expanded":S,"is-radio-button":n});(0,d.useEffect)((()=>{k&&i&&f(o.id)}),[o,k,i,f]);const N=x.name||`search-list-item-${s}`,P=`${N}-${o.id}`,I=(0,d.useCallback)((()=>{f(S?-1:Number(o.id))}),[S,o.id,f]);return k?(0,h.jsx)("div",{className:C,onClick:I,onKeyDown:e=>"Enter"===e.key||" "===e.key?I():null,role:"treeitem",tabIndex:0,children:n?(0,h.jsxs)(h.Fragment,{children:[(0,h.jsx)("input",{type:"radio",id:P,name:N,value:o.value,onChange:a(o),onClick:e=>e.stopPropagation(),checked:i,className:"woocommerce-search-list__item-input",...x}),(0,h.jsx)(_,{item:o,search:p}),j?(0,h.jsx)(v,{label:e||o.count}):null]}):(0,h.jsxs)(h.Fragment,{children:[(0,h.jsx)(c.CheckboxControl,{className:"woocommerce-search-list__item-input",checked:i,...!i&&o.children.some((e=>w.find((t=>t.id===e.id))))?{indeterminate:!0}:{},label:g((0,u.decodeEntities)(o.name),p),onChange:()=>{i?a(m(w,o.children,"id"))():a(function(e,t){const r=m(t,e,"id");return[...e,...r]}(w,o.children))()},onClick:e=>e.stopPropagation()}),j?(0,h.jsx)(v,{label:e||o.count}):null]})}):(0,h.jsxs)("label",{htmlFor:P,className:C,children:[n?(0,h.jsxs)(h.Fragment,{children:[(0,h.jsx)("input",{...x,type:"radio",id:P,name:N,value:o.value,onChange:a(o),checked:i,className:"woocommerce-search-list__item-input"}),(0,h.jsx)(_,{item:o,search:p})]}):(0,h.jsx)(c.CheckboxControl,{...x,id:P,name:N,className:"woocommerce-search-list__item-input",value:(0,u.decodeEntities)(o.value),label:g((0,u.decodeEntities)(o.name),p),onChange:a(o),checked:i}),j?(0,h.jsx)(v,{label:e||o.count}):null]})},x=b,y=window.wc.wcTypes;var f=r(2624),j=r(9491),R=r(3028);r(5022);const k=({id:e,label:t,popoverContents:r,remove:s,screenReaderLabel:i,className:a=""})=>{const[m,p]=(0,d.useState)(!1),w=(0,j.useInstanceId)(k);if(i=i||t,!t)return null;t=(0,u.decodeEntities)(t);const g=(0,l.A)("woocommerce-tag",a,{"has-remove":!!s}),v=`woocommerce-tag__label-${w}`,_=(0,h.jsxs)(h.Fragment,{children:[(0,h.jsx)("span",{className:"screen-reader-text",children:i}),(0,h.jsx)("span",{"aria-hidden":"true",children:t})]});return(0,h.jsxs)("span",{className:g,children:[r?(0,h.jsx)(c.Button,{className:"woocommerce-tag__text",id:v,onClick:()=>p(!0),children:_}):(0,h.jsx)("span",{className:"woocommerce-tag__text",id:v,children:_}),r&&m&&(0,h.jsx)(c.Popover,{onClose:()=>p(!1),children:r}),s&&(0,h.jsx)(c.Button,{className:"woocommerce-tag__remove",onClick:s(e),label:(0,n.sprintf)(
// Translators: %s label.
// Translators: %s label.
(0,n.__)("Remove %s","woocommerce"),t),"aria-describedby":v,children:(0,h.jsx)(o.A,{icon:R.A,size:20,className:"clear-icon",role:"img"})})]})},S=k;r(1939);const C=e=>(0,h.jsx)(x,{...e}),N=e=>{const{list:t,selected:r,renderItem:s,depth:o=0,onSelect:i,instanceId:n,isSingle:a,search:c,useExpandedPanelId:l}=e,[m]=l;return t?(0,h.jsx)(h.Fragment,{children:t.map((t=>{const u=t.children?.length&&!a?t.children.every((({id:e})=>r.find((t=>t.id===e)))):!!r.find((({id:e})=>e===t.id)),p=t.children?.length&&m===t.id;return(0,h.jsxs)(d.Fragment,{children:[(0,h.jsx)("li",{children:s({item:t,isSelected:u,onSelect:i,isSingle:a,selected:r,search:c,depth:o,useExpandedPanelId:l,controlId:n})}),p?(0,h.jsx)(N,{...e,list:t.children,depth:o+1}):null]},t.id)}))}):null},P=({isLoading:e,isSingle:t,selected:r,messages:s,onChange:o,onRemove:i})=>{if(e||t||!r)return null;const a=r.length;return(0,h.jsxs)("div",{className:"woocommerce-search-list__selected",children:[(0,h.jsxs)("div",{className:"woocommerce-search-list__selected-header",children:[(0,h.jsx)("strong",{children:s.selected(a)}),a>0?(0,h.jsx)(c.Button,{variant:"link",isDestructive:!0,onClick:()=>o([]),"aria-label":s.clear,children:(0,n.__)("Clear all","woocommerce")}):null]}),a>0?(0,h.jsx)("ul",{children:r.map(((e,t)=>(0,h.jsx)("li",{children:(0,h.jsx)(S,{label:e.name,id:e.id,remove:i})},t)))}):null]})},I=({filteredList:e,search:t,onSelect:r,instanceId:s,useExpandedPanelId:i,...a})=>{const{messages:c,renderItem:l,selected:d,isSingle:m}=a,u=l||C;return 0===e.length?(0,h.jsxs)("div",{className:"woocommerce-search-list__list is-not-found",children:[(0,h.jsx)("span",{className:"woocommerce-search-list__not-found-icon",children:(0,h.jsx)(o.A,{icon:f.A,role:"img"})}),(0,h.jsx)("span",{className:"woocommerce-search-list__not-found-text",children:t?(0,n.sprintf)(c.noResults,t):c.noItems})]}):(0,h.jsx)("ul",{className:"woocommerce-search-list__list",children:(0,h.jsx)(N,{useExpandedPanelId:i,list:e,selected:d,renderItem:u,onSelect:r,instanceId:s,isSingle:m,search:t})})},L=e=>{const{className:t="",isCompact:r,isHierarchical:s,isLoading:o,isSingle:i,list:a,messages:m=p,onChange:u,onSearch:g,selected:v,type:_="text",debouncedSpeak:b}=e,[x,y]=(0,d.useState)(""),f=(0,d.useState)(-1),R=(0,j.useInstanceId)(L),k=(0,d.useMemo)((()=>({...p,...m})),[m]),S=(0,d.useMemo)((()=>((e,t,r)=>{if(!t)return r?w(e):e;const s=new RegExp(t.replace(/[-\/\\^$*+?.()|[\]{}]/g,"\\$&"),"i"),o=e.map((e=>!!s.test(e.name)&&e)).filter(Boolean);return r?w(o,e):o})(a,x,s)),[a,x,s]);(0,d.useEffect)((()=>{b&&b(k.updated)}),[b,k]),(0,d.useEffect)((()=>{"function"==typeof g&&g(x)}),[x,g]);const C=(0,d.useCallback)((e=>()=>{i&&u([]);const t=v.findIndex((({id:t})=>t===e));u([...v.slice(0,t),...v.slice(t+1)])}),[i,v,u]),N=(0,d.useCallback)((e=>()=>{Array.isArray(e)?u(e):-1===v.findIndex((({id:t})=>t===e.id))?u(i?[e]:[...v,e]):C(e.id)()}),[i,C,u,v]),A=(0,d.useCallback)((e=>{const[t]=v.filter((t=>!e.find((e=>t.id===e.id))));C(t.id)()}),[C,v]);return(0,h.jsxs)("div",{className:(0,l.A)("woocommerce-search-list",t,{"is-compact":r,"is-loading":o,"is-token":"token"===_}),children:["text"===_&&(0,h.jsx)(P,{...e,onRemove:C,messages:k}),(0,h.jsx)("div",{className:"woocommerce-search-list__search",children:"text"===_?(0,h.jsx)(c.TextControl,{label:k.search,type:"search",value:x,onChange:e=>y(e)}):(0,h.jsx)(c.FormTokenField,{disabled:o,label:k.search,onChange:A,onInputChange:e=>y(e),suggestions:[],__experimentalValidateInput:()=>!1,value:o?[(0,n.__)("Loading…","woocommerce")]:v.map((e=>({...e,value:e.name}))),__experimentalShowHowTo:!1})}),o?(0,h.jsx)("div",{className:"woocommerce-search-list__list",children:(0,h.jsx)(c.Spinner,{})}):(0,h.jsx)(I,{...e,search:x,filteredList:S,messages:k,onSelect:N,instanceId:R,useExpandedPanelId:f})]})},A=((0,c.withSpokenMessages)(L),e=>t=>{let{selected:r}=t;r=void 0===r?null:r;const s=null===r;return Array.isArray(r)?(0,h.jsx)(e,{...t}):(0,h.jsx)(e,{...t,selected:s?[]:[r]})}),E=window.wc.wcSettings,O=(0,E.getSetting)("wcBlocksConfig",{pluginUrl:"",productCount:0,defaultAvatar:"",restApiRoutes:{},wordCountType:"words"}),T=(O.pluginUrl,O.pluginUrl,E.STORE_PAGES.shop,E.STORE_PAGES.checkout,E.STORE_PAGES.checkout,E.STORE_PAGES.privacy,E.STORE_PAGES.privacy,E.STORE_PAGES.terms,E.STORE_PAGES.terms,E.STORE_PAGES.cart,E.STORE_PAGES.cart,E.STORE_PAGES.myaccount?.permalink?E.STORE_PAGES.myaccount.permalink:(0,E.getSetting)("wpLoginUrl","/wp-login.php"),(0,E.getSetting)("localPickupEnabled",!1),(0,E.getSetting)("shippingMethodsExist",!1),(0,E.getSetting)("shippingEnabled",!0),(0,E.getSetting)("countries",{})),M=(0,E.getSetting)("countryData",{}),D={...Object.fromEntries(Object.keys(M).filter((e=>!0===M[e].allowBilling)).map((e=>[e,T[e]||""]))),...Object.fromEntries(Object.keys(M).filter((e=>!0===M[e].allowShipping)).map((e=>[e,T[e]||""])))},$=(Object.fromEntries(Object.keys(D).map((e=>[e,M[e].states||{}]))),Object.fromEntries(Object.keys(D).map((e=>[e,M[e].locale||{}]))),{address:["first_name","last_name","company","address_1","address_2","city","postcode","country","state","phone"],contact:["email"],order:[]}),F=((0,E.getSetting)("addressFieldsLocations",$).address,(0,E.getSetting)("addressFieldsLocations",$).contact,(0,E.getSetting)("addressFieldsLocations",$).order,(0,E.getSetting)("additionalOrderFields",{}),(0,E.getSetting)("additionalContactFields",{}),(0,E.getSetting)("additionalAddressFields",{}),window.wp.url),B=window.wp.apiFetch;var H=r.n(B);const G=({selected:e=[],search:t="",queryArgs:r={}})=>{const s=(({selected:e=[],search:t="",queryArgs:r={}})=>{const s=O.productCount>100,o={per_page:s?100:0,catalog_visibility:"any",search:t,orderby:"title",order:"asc"},i=[(0,F.addQueryArgs)("/wc/store/v1/products",{...o,...r})];return s&&e.length&&i.push((0,F.addQueryArgs)("/wc/store/v1/products",{catalog_visibility:"any",include:e,per_page:0})),i})({selected:e,search:t,queryArgs:r});return Promise.all(s.map((e=>H()({path:e})))).then((e=>{const t=((e,t)=>{const r=new Map;return e.filter((e=>{const s=t(e);return!r.has(s)&&(r.set(s,e),!0)}))})(e.flat(),(e=>e.id));return t.map((e=>({...e,parent:0})))})).catch((e=>{throw e}))};var V=r(4347);const W=async e=>{if(!("json"in e))return{code:e.code||"",message:e.message,type:e.type||"general"};try{const t=await e.json();return{code:t.code||"",message:t.message,type:t.type||"api"}}catch(e){return{message:e.message,type:"general"}}};var U=r(923),J=r.n(U);const q=(0,j.createHigherOrderComponent)((e=>{class t extends d.Component{state={error:null,loading:!1,variations:{}};componentDidMount(){const{selected:e,showVariations:t}=this.props;e&&t&&this.loadVariations()}componentDidUpdate(e){const{isLoading:t,selected:r,showVariations:s}=this.props;s&&(!J()(e.selected,r)||e.isLoading&&!t)&&this.loadVariations()}loadVariations=()=>{const{products:e}=this.props,{loading:t,variations:r}=this.state;if(t)return;const s=this.getExpandedProduct();if(!s||r[s])return;const o=e.find((e=>e.id===s));var i;o?.variations&&0!==o.variations.length?(this.setState({loading:!0}),(i=s,H()({path:(0,F.addQueryArgs)("wc/store/v1/products",{per_page:0,type:"variation",parent:i})})).then((e=>{const t=e.map((e=>({...e,parent:s})));this.setState({variations:{...this.state.variations,[s]:t},loading:!1,error:null})})).catch((async e=>{const t=await W(e);this.setState({variations:{...this.state.variations,[s]:null},loading:!1,error:t})}))):this.setState({variations:{...this.state.variations,[s]:null},loading:!1,error:null})};isProductId(e){const{products:t}=this.props;return t.some((t=>t.id===e))}findParentProduct(e){const{products:t}=this.props,r=t.filter((t=>t.variations&&t.variations.find((({id:t})=>t===e))));return r[0]?.id}getExpandedProduct(){const{isLoading:e,selected:t,showVariations:r}=this.props;if(!r)return null;let s=t&&t.length?t[0]:null;return s?this.prevSelectedItem=s:!this.prevSelectedItem||e||this.isProductId(this.prevSelectedItem)||(s=this.prevSelectedItem),!e&&s?this.isProductId(s)?s:this.findParentProduct(s):null}render(){const{error:t,isLoading:r}=this.props,{error:s,loading:o,variations:i}=this.state;return(0,h.jsx)(e,{...this.props,error:s||t,expandedProduct:this.getExpandedProduct(),isLoading:r,variations:i,variationsLoading:o})}}return t}),"withProductVariations"),z=e=>{const{id:t,name:r,parent:s}=e;return{id:t,name:r,parent:s,breadcrumbs:[],children:[],details:e,value:e.slug}},Q=window.wp.escapeHtml,Y=({message:e,type:t})=>e?"general"===t?(0,h.jsxs)("span",{children:[(0,n.__)("The following error was returned","woocommerce"),(0,h.jsx)("br",{}),(0,h.jsx)("code",{children:(0,Q.escapeHTML)(e)})]}):"api"===t?(0,h.jsxs)("span",{children:[(0,n.__)("The following error was returned from the API","woocommerce"),(0,h.jsx)("br",{}),(0,h.jsx)("code",{children:(0,Q.escapeHTML)(e)})]}):e:(0,n.__)("An error has prevented the block from being updated.","woocommerce"),K=({error:e})=>(0,h.jsx)("div",{className:"wc-block-error-message",children:Y(e)});var X=r(1609);const Z=({className:e,item:t,isSelected:r,isLoading:s,onSelect:o,disabled:i,...n})=>(0,h.jsxs)(h.Fragment,{children:[(0,X.createElement)(b,{...n,key:t.id,className:e,isSelected:r,item:t,onSelect:o,disabled:i}),r&&s&&(0,h.jsx)("div",{className:(0,l.A)("woocommerce-search-list__item","woocommerce-product-attributes__item","depth-1","is-loading","is-not-active"),children:(0,h.jsx)(c.Spinner,{})},"loading")]});r(5653);const ee={list:(0,n.__)("Products","woocommerce"),noItems:(0,n.__)("Your store doesn't have any products.","woocommerce"),search:(0,n.__)("Search for a product to display","woocommerce"),updated:(0,n.__)("Product search results updated.","woocommerce")},te=A((re=q((0,j.withInstanceId)((e=>{const{expandedProduct:t=null,error:r,instanceId:s,isCompact:o=!1,isLoading:i,onChange:a,onSearch:c,products:d,renderItem:m,selected:u=[],showVariations:p=!1,variations:w,variationsLoading:g}=e;if(r)return(0,h.jsx)(K,{error:r});const v=[...d,...w&&t&&w[t]?w[t]:[]].map(z);return(0,h.jsx)(L,{className:"woocommerce-products",list:v,isCompact:o,isLoading:i,isSingle:!0,selected:v.filter((({id:e})=>u.includes(Number(e)))),onChange:a,renderItem:m||(p?e=>{const{item:t,search:r,depth:o=0,isSelected:a,onSelect:c}=e,d=t.details?.variations&&Array.isArray(t.details.variations)?t.details.variations.length:0,m=(0,l.A)("woocommerce-search-product__item","woocommerce-search-list__item",`depth-${o}`,"has-count",{"is-searching":r.length>0,"is-skip-level":0===o&&0!==t.parent,"is-variable":d>0});if(!t.breadcrumbs.length){const r=t.details?.variations&&t.details.variations.length>0;return(0,h.jsx)(Z,{...e,className:(0,l.A)(m,{"is-selected":a}),isSelected:a,item:t,onSelect:()=>()=>{c(t)()},isLoading:i||g,countLabel:r?(0,n.sprintf)(/* translators: %1$d is the number of variations of a product product. */ /* translators: %1$d is the number of variations of a product product. */
(0,n.__)("%1$d variations","woocommerce"),t.details?.variations.length):null,name:`products-${s}`,"aria-label":r?(0,n.sprintf)(/* translators: %1$s is the product name, %2$d is the number of variations of that product. */ /* translators: %1$s is the product name, %2$d is the number of variations of that product. */
(0,n._n)("%1$s, has %2$d variation","%1$s, has %2$d variations",t.details?.variations?.length,"woocommerce"),t.name,t.details?.variations.length):void 0})}const u=(0,y.isEmpty)(t.details?.variation)?e:{...e,item:{...e.item,name:t.details?.variation},"aria-label":`${t.breadcrumbs[0]}: ${t.details?.variation}`};return(0,h.jsx)(b,{...u,className:m,name:`variations-${s}`})}:void 0),onSearch:c,messages:{...ee,...e.messages},isHierarchical:!0})}))),({selected:e,...t})=>{const[r,s]=(0,d.useState)(!0),[o,i]=(0,d.useState)(null),[n,a]=(0,d.useState)([]),c=O.productCount>100,l=async e=>{const t=await W(e);i(t),s(!1)},m=(0,d.useRef)(e);(0,d.useEffect)((()=>{G({selected:m.current}).then((e=>{a(e),s(!1)})).catch(l)}),[m]);const u=(0,V.YQ)((t=>{G({selected:e,search:t}).then((e=>{a(e),s(!1)})).catch(l)}),400),p=(0,d.useCallback)((e=>{s(!0),u(e)}),[s,u]);return(0,h.jsx)(re,{...t,selected:e,error:o,products:n,isLoading:r,onSearch:c?p:null})}));var re,se=r(2098);r(3120);const oe=({className:e="",error:t,isLoading:r=!1,onRetry:s})=>(0,h.jsxs)(c.Placeholder,{icon:(0,h.jsx)(o.A,{icon:se.A}),label:(0,n.__)("Sorry, an error occurred","woocommerce"),className:(0,l.A)("wc-block-api-error",e),children:[(0,h.jsx)(K,{error:t}),s&&(0,h.jsx)(h.Fragment,{children:r?(0,h.jsx)(c.Spinner,{}):(0,h.jsx)(c.Button,{variant:"secondary",onClick:s,children:(0,n.__)("Retry","woocommerce")})})]});r(6919);var ie=r(3240),ne=r.n(ie);const ae=["a","b","em","i","strong","p","br"],ce=["target","href","rel","name","download"],le=(e,t)=>{const r=t?.tags||ae,s=t?.attr||ce;return ne().sanitize(e,{ALLOWED_TAGS:r,ALLOWED_ATTR:s})},de=({label:e,screenReaderLabel:t,wrapperElement:r,wrapperProps:s={},allowHTML:o=!1})=>{let i;const n=null!=e,a=null!=t;return!n&&a?(i=r||"span",s={...s,className:(0,l.A)(s.className,"screen-reader-text")},(0,h.jsx)(i,{...s,children:t})):(i=r||d.Fragment,n&&a&&e!==t?(0,h.jsxs)(i,{...s,children:[o?(0,h.jsx)(d.RawHTML,{children:le(e,{tags:["b","em","i","strong","p","br","span"],attr:["style"]})}):(0,h.jsx)("span",{"aria-hidden":"true",children:e}),(0,h.jsx)("span",{className:"screen-reader-text",children:t})]}):(0,h.jsx)(i,{...s,children:e}))},me=({onClick:e,label:t=(0,n.__)("Load more","woocommerce"),screenReaderLabel:r=(0,n.__)("Load more","woocommerce")})=>(0,h.jsx)("div",{className:"wp-block-button wc-block-load-more wc-block-components-load-more",children:(0,h.jsx)("button",{className:"wp-block-button__link",onClick:e,children:(0,h.jsx)(de,{label:t,screenReaderLabel:r})})}),ue=window.wc.blocksComponents;r(6878);const he=({onChange:e,readOnly:t,value:r})=>(0,h.jsx)(ue.SortSelect,{className:"wc-block-review-sort-select wc-block-components-review-sort-select",label:(0,n.__)("Order by","woocommerce"),onChange:e,options:[{key:"most-recent",label:(0,n.__)("Most recent","woocommerce")},{key:"highest-rating",label:(0,n.__)("Highest rating","woocommerce")},{key:"lowest-rating",label:(0,n.__)("Lowest rating","woocommerce")}],readOnly:t,screenReaderLabel:(0,n.__)("Order reviews by","woocommerce"),value:r});function pe(e){let t,r,s,o=[];for(let i=0;i<e.length;i++)t=e.substring(i),r=t.match(/^&[a-z0-9#]+;/),r?(s=r[0],o.push(s),i+=s.length-1):o.push(e[i]);return o}const we=(e,t,r="...")=>{const s=function(e,t){const r=(t=t||{}).limit||100,s=void 0===t.preserveTags||t.preserveTags,o=void 0!==t.wordBreak&&t.wordBreak,i=t.suffix||"...",n=t.moreLink||"",a=t.moreText||"»",c=t.preserveWhiteSpace||!1,l=e.replace(/</g,"\n<").replace(/>/g,">\n").replace(/\n\n/g,"\n").replace(/^\n/g,"").replace(/\n$/g,"").split("\n");let d,m,u,h,p,w,g=0,v=[],_=!1;for(let e=0;e<l.length;e++){if(d=l[e],h=c?d:d.replace(/[ ]+/g," "),!d.length)continue;const t=pe(h);if("<"!==d[0])if(g>=r)d="";else if(g+t.length>=r){if(m=r-g," "===t[m-1])for(;m&&(m-=1," "===t[m-1]););else u=t.slice(m).indexOf(" "),o||(-1!==u?m+=u:m=d.length);if(d=t.slice(0,m).join("")+i,n){const e=document.createElement("a");e.href=n,e.style.display="inline",e.textContent=a,d+=e.outerHTML}g=r,_=!0}else g+=t.length;else if(s){if(g>=r)if(p=d.match(/[a-zA-Z]+/),w=p?p[0]:"",w)if("</"!==d.substring(0,2))v.push(w),d="";else{for(;v[v.length-1]!==w&&v.length;)v.pop();v.length&&(d=""),v.pop()}else d=""}else d="";l[e]=d}return{html:l.join("\n").replace(/\n/g,""),more:_}}(e,{suffix:r,limit:t});return s.html},ge=(e,t,r)=>(t<=r?e.start=e.middle+1:e.end=e.middle-1,e),ve=(e,t,r,s)=>{const o=((e,t,r)=>{let s={start:0,middle:0,end:e.length};for(;s.start<=s.end;)s.middle=Math.floor((s.start+s.end)/2),t.innerHTML=we(e,s.middle),s=ge(s,t.clientHeight,r);return s.middle})(e,t,r);return we(e,o-s.length,s)},_e={className:"read-more-content",ellipsis:"&hellip;",lessText:(0,n.__)("Read less","woocommerce"),maxLines:3,moreText:(0,n.__)("Read more","woocommerce")};class be extends d.Component{static defaultProps=_e;constructor(e){super(e),this.state={isExpanded:!1,clampEnabled:null,content:e.children,summary:"."},this.reviewContent=(0,d.createRef)(),this.reviewSummary=(0,d.createRef)(),this.getButton=this.getButton.bind(this),this.onClick=this.onClick.bind(this)}componentDidMount(){this.setSummary()}componentDidUpdate(e){e.maxLines===this.props.maxLines&&e.children===this.props.children||this.setState({clampEnabled:null,summary:"."},this.setSummary)}setSummary(){if(this.props.children){const{maxLines:e,ellipsis:t}=this.props;if(!this.reviewSummary.current||!this.reviewContent.current)return;const r=(this.reviewSummary.current.clientHeight+1)*e+1,s=this.reviewContent.current.clientHeight+1>r;this.setState({clampEnabled:s}),s&&this.setState({summary:ve(this.reviewContent.current.innerHTML,this.reviewSummary.current,r,t)})}}getButton(){const{isExpanded:e}=this.state,{className:t,lessText:r,moreText:s}=this.props,o=e?r:s;if(o)return(0,h.jsx)("a",{href:"#more",className:t+"__read_more",onClick:this.onClick,"aria-expanded":!e,role:"button",children:o})}onClick(e){e.preventDefault();const{isExpanded:t}=this.state;this.setState({isExpanded:!t})}render(){const{className:e}=this.props,{content:t,summary:r,clampEnabled:s,isExpanded:o}=this.state;return t?!1===s?(0,h.jsx)("div",{className:e,children:(0,h.jsx)("div",{ref:this.reviewContent,children:t})}):(0,h.jsxs)("div",{className:e,children:[(!o||null===s)&&(0,h.jsx)("div",{ref:this.reviewSummary,"aria-hidden":o,dangerouslySetInnerHTML:{__html:r}}),(o||null===s)&&(0,h.jsx)("div",{ref:this.reviewContent,"aria-hidden":!o,children:t}),this.getButton()]}):null}}const xe=be;function ye(e,t,r){return r||!e?(0,h.jsx)("div",{className:"wc-block-review-list-item__image wc-block-components-review-list-item__image"}):(0,h.jsxs)("div",{className:"wc-block-review-list-item__image wc-block-components-review-list-item__image",children:["product"===t?(0,h.jsx)("img",{"aria-hidden":"true",alt:e.product_image?.alt||"",src:e.product_image?.thumbnail||""}):(0,h.jsx)("img",{"aria-hidden":"true",alt:"",src:e.reviewer_avatar_urls[96]||""}),e.verified&&(0,h.jsx)("div",{className:"wc-block-review-list-item__verified wc-block-components-review-list-item__verified",title:(0,n.__)("Verified buyer","woocommerce"),children:(0,n.__)("Verified buyer","woocommerce")})]})}function fe(e){return(0,h.jsx)(xe,{maxLines:10,moreText:(0,n.__)("Read full review","woocommerce"),lessText:(0,n.__)("Hide full review","woocommerce"),className:"wc-block-review-list-item__text wc-block-components-review-list-item__text",children:(0,h.jsx)("div",{dangerouslySetInnerHTML:{__html:e.review||""}})})}function je(e,t){return(0,h.jsx)("div",{className:"wc-block-review-list-item__product wc-block-components-review-list-item__product",children:(0,h.jsx)("a",{href:e.product_permalink,"aria-labelledby":t,children:(0,u.decodeEntities)(e.product_name)})})}function Re(e){const{reviewer:t=""}=e;return(0,h.jsx)("div",{className:"wc-block-review-list-item__author wc-block-components-review-list-item__author",children:t})}function ke(e){const{date_created:t,formatted_date_created:r}=e;return(0,h.jsx)("time",{className:"wc-block-review-list-item__published-date wc-block-components-review-list-item__published-date",dateTime:t,children:r})}function Se(e,t){const{rating:r}=e,s={width:r/5*100+"%"},o=(0,n.sprintf)(/* translators: %f is referring to the average rating value */ /* translators: %f is referring to the average rating value */
(0,n.__)("Rated %f out of 5","woocommerce"),r),i={__html:(0,n.sprintf)(/* translators: %s is referring to the average rating value */ /* translators: %s is referring to the average rating value */
(0,n.__)("Rated %s out of 5","woocommerce"),(0,n.sprintf)('<strong class="rating">%f</strong>',r))};return(0,h.jsx)("div",{id:t,"aria-label":`${(0,u.decodeEntities)(e.product_name)} ${o}`,className:"wc-block-review-list-item__rating wc-block-components-review-list-item__rating",children:(0,h.jsx)("div",{"aria-hidden":"true",className:`wc-block-review-list-item__rating__stars wc-block-components-review-list-item__rating__stars wc-block-review-list-item__rating__stars--${r}`,role:"img",children:(0,h.jsx)("span",{style:s,dangerouslySetInnerHTML:i})})})}r(7313);const Ce=({attributes:e,review:t={}})=>{const{imageType:r,showReviewDate:s,showReviewerName:o,showReviewImage:i,showReviewRating:n,showReviewContent:a,showProductName:c}=e,{rating:m}=t,u=!(Object.keys(t).length>0),p=Number.isFinite(m)&&n,w=(0,d.useId)();return(0,h.jsxs)("li",{className:(0,l.A)("wc-block-review-list-item__item","wc-block-components-review-list-item__item",{"is-loading":u,"wc-block-components-review-list-item__item--has-image":i}),"aria-hidden":u,children:[(c||s||o||i||p)&&(0,h.jsxs)("div",{className:"wc-block-review-list-item__info wc-block-components-review-list-item__info",children:[i&&ye(t,r,u),(c||o||p||s)&&(0,h.jsxs)("div",{className:"wc-block-review-list-item__meta wc-block-components-review-list-item__meta",children:[p&&Se(t,w),c&&je(t,w),o&&Re(t),s&&ke(t)]})]}),a&&fe(t)]})};r(5183);const Ne=({attributes:e,reviews:t})=>{const r=(0,E.getSetting)("showAvatars",!0),s=(0,E.getSetting)("reviewRatingsEnabled",!0),o=(r||"product"===e.imageType)&&e.showReviewImage,i=s&&e.showReviewRating,n={...e,showReviewImage:o,showReviewRating:i};return(0,h.jsx)("ul",{className:"wc-block-review-list wc-block-components-review-list",children:0===t.length?(0,h.jsx)(Ce,{attributes:n}):t.map(((e,t)=>(0,h.jsx)(Ce,{attributes:n,review:e},e.id||t)))})},Pe=e=>{const{className:t,categoryIds:r,productId:s,showReviewDate:o,showReviewerName:i,showReviewContent:n,showProductName:a,showReviewImage:c,showReviewRating:d}=e;let m="wc-block-all-reviews";return s&&(m="wc-block-reviews-by-product"),Array.isArray(r)&&(m="wc-block-reviews-by-category"),(0,l.A)(m,t,{"has-image":c,"has-name":i,"has-date":o,"has-rating":d,"has-content":n,"has-product-name":a})},Ie=e=>{const{categoryIds:t,imageType:r,orderby:s,productId:o,reviewsOnPageLoad:i,reviewsOnLoadMore:n,showLoadMore:a,showOrderby:c}=e,l={"data-image-type":r,"data-orderby":s,"data-reviews-on-page-load":i,"data-reviews-on-load-more":n,"data-show-load-more":a,"data-show-orderby":c};return o&&(l["data-product-id"]=o),Array.isArray(t)&&(l["data-category-ids"]=t.join(",")),l};class Le extends d.Component{render(){const{attributes:e,error:t,isLoading:r,noReviewsPlaceholder:s,reviews:o,totalReviews:i}=this.props;if(t)return(0,h.jsx)(oe,{className:"wc-block-featured-product-error",error:t,isLoading:r});if(0===o.length&&!r)return(0,h.jsx)(s,{attributes:e});const a=(0,E.getSetting)("reviewRatingsEnabled",!0);return(0,h.jsxs)(c.Disabled,{children:[e.showOrderby&&a&&(0,h.jsx)(he,{readOnly:!0,value:e.orderby,onChange:()=>null}),(0,h.jsx)(Ne,{attributes:e,reviews:o}),e.showLoadMore&&i>o.length&&(0,h.jsx)(me,{screenReaderLabel:(0,n.__)("Load more reviews","woocommerce"),onClick:()=>null})]})}}const Ae=(e=>{var t;class r extends d.Component{isPreview=!!this.props.attributes.previewReviews;delayedAppendReviews=(null!==(t=this.props.delayFunction)&&void 0!==t?t:e=>e)(this.appendReviews);isMounted=!1;state={error:null,loading:!0,reviews:this.isPreview&&this.props.attributes?.previewReviews?this.props.attributes.previewReviews:[],totalReviews:this.isPreview&&this.props.attributes?.previewReviews?this.props.attributes.previewReviews.length:0};componentDidMount(){this.isMounted=!0,this.replaceReviews()}componentDidUpdate(e){e.reviewsToDisplay<this.props.reviewsToDisplay?this.delayedAppendReviews():this.shouldReplaceReviews(e,this.props)&&this.replaceReviews()}shouldReplaceReviews(e,t){return e.orderby!==t.orderby||e.order!==t.order||e.productId!==t.productId||!J()(e.categoryIds,t.categoryIds)}componentWillUnmount(){this.isMounted=!1,"cancel"in this.delayedAppendReviews&&"function"==typeof this.delayedAppendReviews.cancel&&this.delayedAppendReviews.cancel()}getArgs(e){const{categoryIds:t,order:r,orderby:s,productId:o,reviewsToDisplay:i}=this.props,n={order:r,orderby:s,per_page:i-e,offset:e};if(t){const e=Array.isArray(t)?t:JSON.parse(t);n.category_id=Array.isArray(e)?e.join(","):e}return o&&(n.product_id=o),n}replaceReviews(){var e;if(this.isPreview)return;const t=null!==(e=this.props.onReviewsReplaced)&&void 0!==e?e:()=>{};this.updateListOfReviews().then(t)}appendReviews(){var e;if(this.isPreview)return;const t=null!==(e=this.props.onReviewsAppended)&&void 0!==e?e:()=>{},{reviewsToDisplay:r}=this.props,{reviews:s}=this.state;r<=s.length||this.updateListOfReviews(s).then(t)}updateListOfReviews(e=[]){const{reviewsToDisplay:t}=this.props,{totalReviews:r}=this.state,s=Math.min(r,t)-e.length;return this.setState({loading:!0,reviews:e.concat(Array(s).fill({}))}),(o=this.getArgs(e.length),H()({path:"/wc/store/v1/products/reviews?"+Object.entries(o).map((e=>e.join("="))).join("&"),parse:!1}).then((e=>e.json().then((t=>({reviews:t,totalReviews:parseInt(e.headers.get("x-wp-total"),10)})))))).then((({reviews:t,totalReviews:r})=>(this.isMounted&&this.setState({reviews:e.filter((e=>Object.keys(e).length)).concat(t),totalReviews:r,loading:!1,error:null}),{newReviews:t}))).catch(this.setError);var o}setError=async e=>{var t;if(!this.isMounted)return;const r=null!==(t=this.props.onReviewsLoadError)&&void 0!==t?t:()=>{},s=await W(e);this.setState({reviews:[],loading:!1,error:s}),r(s)};render(){const{reviewsToDisplay:t}=this.props,{error:r,loading:s,reviews:o,totalReviews:i}=this.state;return(0,h.jsx)(e,{...this.props,error:r,isLoading:s,reviews:o.slice(0,t),totalReviews:i})}}const{displayName:s=e.name||"Component"}=e;return r.displayName=`WithReviews(${s})`,r})(Le),Ee=({attributes:e,icon:t,name:r,noReviewsPlaceholder:s})=>{const{categoryIds:o,productId:i,reviewsOnPageLoad:a,showProductName:l,showReviewDate:d,showReviewerName:m,showReviewContent:u,showReviewImage:p,showReviewRating:w}=e,{order:g,orderby:v}=(e=>{if((0,E.getSetting)("reviewRatingsEnabled",!0)){if("lowest-rating"===e)return{order:"asc",orderby:"rating"};if("highest-rating"===e)return{order:"desc",orderby:"rating"}}return{order:"desc",orderby:"date_gmt"}})(e.orderby);return u||w||d||m||p||l?(0,h.jsx)(h.Fragment,{children:(0,h.jsx)(Ae,{attributes:e,categoryIds:o,delayFunction:e=>(e=>{let t,r=null;const s=(...s)=>{r=s,t&&clearTimeout(t),t=setTimeout((()=>{t=null,r&&e(...r)}),400)};return s.flush=()=>{t&&r&&(e(...r),clearTimeout(t),t=null)},s.clear=()=>{t&&clearTimeout(t),t=null},s})(e),noReviewsPlaceholder:s,orderby:v,order:g,productId:i,reviewsToDisplay:a})}):(0,h.jsx)(c.Placeholder,{icon:t,label:r,children:(0,n.__)("The content for this block is hidden due to block settings.","woocommerce")})},Oe=(0,j.createHigherOrderComponent)((e=>class extends d.Component{state={error:null,loading:!1,product:"preview"===this.props.attributes.productId?this.props.attributes.previewProduct:null};componentDidMount(){this.loadProduct()}componentDidUpdate(e){e.attributes.productId!==this.props.attributes.productId&&this.loadProduct()}loadProduct=()=>{const{productId:e}=this.props.attributes;"preview"!==e&&(e?(this.setState({loading:!0}),(e=>H()({path:`/wc/store/v1/products/${e}`}))(e).then((e=>{this.setState({product:e,loading:!1,error:null})})).catch((async e=>{const t=await W(e);this.setState({product:null,loading:!1,error:t})}))):this.setState({product:null,loading:!1,error:null}))};render(){const{error:t,loading:r,product:s}=this.state;return(0,h.jsx)(e,{...this.props,error:t,getProduct:this.loadProduct,isLoading:r,product:s})}}),"withProduct")((({error:e,getProduct:t,isLoading:r,product:s})=>{if(e)return(0,h.jsx)(oe,{className:"wc-block-featured-product-error",error:e,isLoading:r,onRetry:t});const a=!s||r?(0,h.jsx)(c.Spinner,{}):(0,n.sprintf)(/* translators: %s is the product name. */ /* translators: %s is the product name. */
(0,n.__)("This block lists reviews for a selected product. %s doesn't have any reviews yet, but they will show up here when it does.","woocommerce"),(0,u.decodeEntities)(s.name));return(0,h.jsx)(c.Placeholder,{className:"wc-block-reviews-by-product",icon:(0,h.jsx)(o.A,{icon:i.A,className:"block-editor-block-icon"}),label:(0,n.__)("Reviews by Product","woocommerce"),children:a})})),Te=(e,t,r)=>(0,h.jsx)(a.BlockControls,{children:(0,h.jsx)(c.ToolbarGroup,{controls:[{icon:"edit",title:r,onClick:()=>t({editMode:!e}),isActive:e}]})}),Me=(e,t)=>{const r=(0,E.getSetting)("showAvatars",!0),s=(0,E.getSetting)("reviewRatingsEnabled",!0);return(0,h.jsxs)(h.Fragment,{children:[(0,h.jsx)(c.ToggleControl,{label:(0,n.__)("Product rating","woocommerce"),checked:e.showReviewRating,onChange:()=>t({showReviewRating:!e.showReviewRating})}),e.showReviewRating&&!s&&(0,h.jsx)(c.Notice,{className:"wc-block-base-control-notice",isDismissible:!1,children:(0,d.createInterpolateElement)((0,n.__)("Product rating is disabled in your <a>store settings</a>.","woocommerce"),{a:(0,h.jsx)("a",{href:(0,E.getAdminLink)("admin.php?page=wc-settings&tab=products"),target:"_blank",rel:"noopener noreferrer"})})}),(0,h.jsx)(c.ToggleControl,{label:(0,n.__)("Reviewer name","woocommerce"),checked:e.showReviewerName,onChange:()=>t({showReviewerName:!e.showReviewerName})}),(0,h.jsx)(c.ToggleControl,{label:(0,n.__)("Image","woocommerce"),checked:e.showReviewImage,onChange:()=>t({showReviewImage:!e.showReviewImage})}),(0,h.jsx)(c.ToggleControl,{label:(0,n.__)("Review date","woocommerce"),checked:e.showReviewDate,onChange:()=>t({showReviewDate:!e.showReviewDate})}),(0,h.jsx)(c.ToggleControl,{label:(0,n.__)("Review content","woocommerce"),checked:e.showReviewContent,onChange:()=>t({showReviewContent:!e.showReviewContent})}),e.showReviewImage&&(0,h.jsxs)(h.Fragment,{children:[(0,h.jsxs)(c.__experimentalToggleGroupControl,{label:(0,n.__)("Review image","woocommerce"),isBlock:!0,value:e.imageType,onChange:e=>t({imageType:e}),children:[(0,h.jsx)(c.__experimentalToggleGroupControlOption,{value:"reviewer",label:(0,n.__)("Reviewer photo","woocommerce")}),(0,h.jsx)(c.__experimentalToggleGroupControlOption,{value:"product",label:(0,n.__)("Product","woocommerce")})]}),"reviewer"===e.imageType&&!r&&(0,h.jsx)(c.Notice,{className:"wc-block-base-control-notice",isDismissible:!1,children:(0,d.createInterpolateElement)((0,n.__)("Reviewer photo is disabled in your <a>site settings</a>.","woocommerce"),{a:(0,h.jsx)("a",{href:(0,E.getAdminLink)("options-discussion.php"),target:"_blank",rel:"noopener noreferrer"})})})]})]})},De=(e,t)=>(0,h.jsxs)(h.Fragment,{children:[(0,h.jsx)(c.ToggleControl,{label:(0,n.__)("Order by","woocommerce"),checked:e.showOrderby,onChange:()=>t({showOrderby:!e.showOrderby})}),(0,h.jsx)(c.SelectControl,{label:(0,n.__)("Order Product Reviews by","woocommerce"),value:e.orderby,options:[{label:"Most recent",value:"most-recent"},{label:"Highest Rating",value:"highest-rating"},{label:"Lowest Rating",value:"lowest-rating"}],onChange:e=>t({orderby:e})}),(0,h.jsx)(c.RangeControl,{label:(0,n.__)("Starting Number of Reviews","woocommerce"),value:e.reviewsOnPageLoad,onChange:e=>t({reviewsOnPageLoad:e}),max:20,min:1}),(0,h.jsx)(c.ToggleControl,{label:(0,n.__)("Load more","woocommerce"),checked:e.showLoadMore,onChange:()=>t({showLoadMore:!e.showLoadMore})}),e.showLoadMore&&(0,h.jsx)(c.RangeControl,{label:(0,n.__)("Load More Reviews","woocommerce"),value:e.reviewsOnLoadMore,onChange:e=>t({reviewsOnLoadMore:e}),max:20,min:1})]}),$e=(0,c.withSpokenMessages)((({attributes:e,debouncedSpeak:t,setAttributes:r})=>{const{editMode:s,productId:l}=e,d=(0,a.useBlockProps)(),m=e=>{const{item:t=0}=e;return(0,h.jsx)(b,{...e,item:{...t,count:t.details.review_count},countLabel:(0,n.sprintf)(/* translators: %d is the review count. */ /* translators: %d is the review count. */
(0,n._n)("%d review","%d reviews",t.details.review_count,"woocommerce"),t.details.review_count),"aria-label":(0,n.sprintf)(/* translators: %1$s is the item name, and %2$d is the number of reviews for the item. */ /* translators: %1$s is the item name, and %2$d is the number of reviews for the item. */
(0,n._n)("%1$s, has %2$d review","%1$s, has %2$d reviews",t.details.review_count,"woocommerce"),(0,u.decodeEntities)(t.name),t.details.review_count)})};if(!l||s)return(0,h.jsxs)(c.Placeholder,{icon:(0,h.jsx)(o.A,{icon:i.A,className:"block-editor-block-icon"}),label:(0,n.__)("Reviews by Product","woocommerce"),className:"wc-block-reviews-by-product",children:[(0,n.__)("Show reviews of your product to build trust","woocommerce"),(0,h.jsxs)("div",{className:"wc-block-reviews__selection",children:[(0,h.jsx)(te,{selected:e.productId||0,onChange:(e=[])=>{const t=e[0]?e[0].id:0;r({productId:t})},queryArgs:{orderby:"comment_count",order:"desc"},renderItem:m}),(0,h.jsx)(c.Button,{variant:"primary",onClick:()=>{r({editMode:!1}),t((0,n.__)("Showing Reviews by Product block preview.","woocommerce"))},children:(0,n.__)("Done","woocommerce")})]})]});const p=(0,n.__)("Edit selected product","woocommerce");return(0,h.jsxs)("div",{...d,children:[Te(s,r,p),(0,h.jsxs)(a.InspectorControls,{children:[(0,h.jsx)(c.PanelBody,{title:(0,n.__)("Product","woocommerce"),initialOpen:!1,children:(0,h.jsx)(te,{selected:e.productId||0,onChange:(e=[])=>{const t=e[0]?e[0].id:0;r({productId:t})},renderItem:m,isCompact:!0})}),(0,h.jsx)(c.PanelBody,{title:(0,n.__)("Content","woocommerce"),children:Me(e,r)}),(0,h.jsx)(c.PanelBody,{title:(0,n.__)("List Settings","woocommerce"),children:De(e,r)})]},"inspector"),(0,h.jsx)(Ee,{attributes:e,icon:(0,h.jsx)(o.A,{icon:i.A,className:"block-editor-block-icon"}),name:(0,n.__)("Reviews by Product","woocommerce"),noReviewsPlaceholder:Oe})]})})),Fe={attributes:{editMode:!1,imageType:"reviewer",orderby:"most-recent",reviewsOnLoadMore:10,reviewsOnPageLoad:10,showLoadMore:!0,showOrderby:!0,showReviewDate:!0,showReviewerName:!0,showReviewImage:!0,showReviewRating:!0,showReviewContent:!0,previewReviews:[{id:1,date_created:"2019-07-15T17:05:04",formatted_date_created:(0,n.__)("July 15, 2019","woocommerce"),date_created_gmt:"2019-07-15T15:05:04",product_id:0,product_name:(0,n.__)("WordPress Pennant","woocommerce"),product_permalink:"#",
/* translators: An example person name used for the block previews. */
reviewer:(0,n.__)("Alice","woocommerce"),review:`<p>${(0,n.__)("I bought this product last week and I'm very happy with it.","woocommerce")}</p>\n`,reviewer_avatar_urls:{48:O.defaultAvatar,96:O.defaultAvatar},rating:5,verified:!0},{id:2,date_created:"2019-07-12T12:39:39",formatted_date_created:(0,n.__)("July 12, 2019","woocommerce"),date_created_gmt:"2019-07-12T10:39:39",product_id:0,product_name:(0,n.__)("WordPress Pennant","woocommerce"),product_permalink:"#",
/* translators: An example person name used for the block previews. */
reviewer:(0,n.__)("Bob","woocommerce"),review:`<p>${(0,n.__)("This product is awesome, I love it!","woocommerce")}</p>\n`,reviewer_avatar_urls:{48:O.defaultAvatar,96:O.defaultAvatar},rating:null,verified:!1}]}},Be=JSON.parse('{"$schema":"https://schemas.wp.org/trunk/block.json","apiVersion":3,"name":"woocommerce/reviews-by-product","title":"Reviews by Product","category":"woocommerce","keywords":["WooCommerce"],"description":"Display reviews for your products.","textdomain":"woocommerce","supports":{"html":false,"interactivity":{"clientNavigation":true},"color":{"background":false},"typography":{"fontSize":true}}}');(0,s.registerBlockType)(Be,{icon:{src:(0,h.jsx)(o.A,{icon:i.A,className:"wc-block-editor-components-block-icon"})},example:{...Fe,attributes:{...Fe.attributes,productId:1}},attributes:{editMode:{type:"boolean",default:!0},imageType:{type:"string",default:"reviewer"},orderby:{type:"string",default:"most-recent"},reviewsOnLoadMore:{type:"number",default:10},reviewsOnPageLoad:{type:"number",default:10},showLoadMore:{type:"boolean",default:!0},showOrderby:{type:"boolean",default:!0},showReviewDate:{type:"boolean",default:!0},showReviewerName:{type:"boolean",default:!0},showReviewImage:{type:"boolean",default:!0},showReviewRating:{type:"boolean",default:!0},showReviewContent:{type:"boolean",default:!0},previewReviews:{type:"array",default:null},productId:{type:"number"}},edit:e=>(0,h.jsx)($e,{...e}),save:({attributes:e})=>(0,h.jsx)("div",{...a.useBlockProps.save({className:Pe(e)}),...Ie(e)})})},6919:()=>{},7313:()=>{},5183:()=>{},6878:()=>{},4809:()=>{},3120:()=>{},5653:()=>{},1939:()=>{},5022:()=>{},1609:e=>{"use strict";e.exports=window.React},790:e=>{"use strict";e.exports=window.ReactJSXRuntime},9491:e=>{"use strict";e.exports=window.wp.compose},6087:e=>{"use strict";e.exports=window.wp.element},7723:e=>{"use strict";e.exports=window.wp.i18n},923:e=>{"use strict";e.exports=window.wp.isShallowEqual},5573:e=>{"use strict";e.exports=window.wp.primitives}},o={};function i(e){var t=o[e];if(void 0!==t)return t.exports;var r=o[e]={exports:{}};return s[e].call(r.exports,r,r.exports,i),r.exports}i.m=s,e=[],i.O=(t,r,s,o)=>{if(!r){var n=1/0;for(d=0;d<e.length;d++){for(var[r,s,o]=e[d],a=!0,c=0;c<r.length;c++)(!1&o||n>=o)&&Object.keys(i.O).every((e=>i.O[e](r[c])))?r.splice(c--,1):(a=!1,o<n&&(n=o));if(a){e.splice(d--,1);var l=s();void 0!==l&&(t=l)}}return t}o=o||0;for(var d=e.length;d>0&&e[d-1][2]>o;d--)e[d]=e[d-1];e[d]=[r,s,o]},i.n=e=>{var t=e&&e.__esModule?()=>e.default:()=>e;return i.d(t,{a:t}),t},r=Object.getPrototypeOf?e=>Object.getPrototypeOf(e):e=>e.__proto__,i.t=function(e,s){if(1&s&&(e=this(e)),8&s)return e;if("object"==typeof e&&e){if(4&s&&e.__esModule)return e;if(16&s&&"function"==typeof e.then)return e}var o=Object.create(null);i.r(o);var n={};t=t||[null,r({}),r([]),r(r)];for(var a=2&s&&e;"object"==typeof a&&!~t.indexOf(a);a=r(a))Object.getOwnPropertyNames(a).forEach((t=>n[t]=()=>e[t]));return n.default=()=>e,i.d(o,n),o},i.d=(e,t)=>{for(var r in t)i.o(t,r)&&!i.o(e,r)&&Object.defineProperty(e,r,{enumerable:!0,get:t[r]})},i.o=(e,t)=>Object.prototype.hasOwnProperty.call(e,t),i.r=e=>{"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})},i.j=1890,(()=>{var e={1890:0};i.O.j=t=>0===e[t];var t=(t,r)=>{var s,o,[n,a,c]=r,l=0;if(n.some((t=>0!==e[t]))){for(s in a)i.o(a,s)&&(i.m[s]=a[s]);if(c)var d=c(i)}for(t&&t(r);l<n.length;l++)o=n[l],i.o(e,o)&&e[o]&&e[o][0](),e[o]=0;return i.O(d)},r=globalThis.webpackChunkwebpackWcBlocksMainJsonp=globalThis.webpackChunkwebpackWcBlocksMainJsonp||[];r.forEach(t.bind(null,0)),r.push=t.bind(null,r.push.bind(r))})();var n=i.O(void 0,[94],(()=>i(4604)));n=i.O(n),((this.wc=this.wc||{}).blocks=this.wc.blocks||{})["reviews-by-product"]=n})();
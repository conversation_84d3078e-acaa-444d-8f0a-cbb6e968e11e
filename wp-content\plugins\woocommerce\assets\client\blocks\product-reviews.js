(()=>{"use strict";const e=window.wp.blocks,t=window.wp.data,o=window.wc.wcTypes;class i{blocks=new Map;initialized=!1;attemptedRegisteredBlocks=new Set;constructor(){this.initializeSubscriptions()}static getInstance(){return i.instance||(i.instance=new i),i.instance}parseTemplateId(e){const t=(0,o.isNumber)(e)?void 0:e;return t?.split("//")[1]}initializeSubscriptions(){if(this.initialized)return;const e=(0,t.subscribe)((()=>{const o=(0,t.select)("core/edit-site"),i=(0,t.select)("core/edit-post");if(o||i)if(o){const i=o.getEditedPostId();e(),this.currentTemplateId="string"==typeof i?this.parseTemplateId(i):void 0,(0,t.subscribe)((()=>{const e=this.currentTemplateId;this.currentTemplateId=this.parseTemplateId(o.getEditedPostId()),e!==this.currentTemplateId&&this.handleTemplateChange(e)}),"core/edit-site"),this.initialized=!0}else i&&(e(),this.blocks.forEach((e=>{if(e.isAvailableOnPostEditor){const t=e.variationName||e.blockName;this.hasAttemptedRegistration(t)||this.registerBlock(e)}})),this.initialized=!0)}))}handleTemplateChange(e){(this.currentTemplateId?.includes("single-product")||e?.includes("single-product"))&&this.blocks.forEach((e=>{this.unregisterBlock(e),this.registerBlock(e)}))}hasAttemptedRegistration(e){return this.attemptedRegisteredBlocks.has(e)}unregisterBlock(t){const{blockName:o,isVariationBlock:i,variationName:r}=t;try{i&&r?((0,e.unregisterBlockVariation)(o,r),this.attemptedRegisteredBlocks.delete(r)):((0,e.unregisterBlockType)(o),this.attemptedRegisteredBlocks.delete(o))}catch(e){console.debug(`Failed to unregister block ${o}:`,e)}}registerBlock(i){const{blockName:r,settings:n,isVariationBlock:s,variationName:c,isAvailableOnPostEditor:a}=i;try{const i=c||r;if(this.hasAttemptedRegistration(i))return;const l=(0,t.select)("core/edit-site");if(!l&&!a)return;if(s)(0,e.registerBlockVariation)(r,n);else{const t=(0,o.isEmpty)(n?.ancestor)?["woocommerce/single-product"]:n?.ancestor,i=l&&this.currentTemplateId?.includes("single-product");(0,e.registerBlockType)(r,{...n,ancestor:i?void 0:t})}this.attemptedRegisteredBlocks.add(i)}catch(e){console.error(`Failed to register block ${r}:`,e)}}registerBlockConfig(e){const t=e.variationName||e.blockName;this.blocks.set(t,e),this.registerBlock(e)}}const r=JSON.parse('{"$schema":"https://schemas.wp.org/trunk/block.json","apiVersion":3,"name":"woocommerce/product-reviews","icon":"admin-comments","title":"Product Reviews","description":"Display a product\'s reviews","category":"woocommerce","textdomain":"woocommerce","attributes":{"tagName":{"type":"string","default":"div"}},"supports":{"interactivity":true,"align":["wide","full"],"html":false,"color":{"gradients":true,"heading":true,"link":true,"__experimentalDefaultControls":{"background":true,"text":true,"link":true}},"spacing":{"margin":true,"padding":true},"typography":{"fontSize":true,"lineHeight":true,"__experimentalFontFamily":true,"__experimentalFontWeight":true,"__experimentalFontStyle":true,"__experimentalTextTransform":true,"__experimentalTextDecoration":true,"__experimentalLetterSpacing":true,"__experimentalDefaultControls":{"fontSize":true}},"__experimentalBorder":{"radius":true,"color":true,"width":true,"style":true,"__experimentalDefaultControls":{"radius":true,"color":true,"width":true,"style":true}}},"usesContext":["postId","postType"],"viewScriptModule":"woocommerce/product-reviews"}'),n=window.wp.blockEditor,s=window.ReactJSXRuntime,c=window.wp.components,a=window.wp.i18n,l=[["woocommerce/product-reviews-title"],["woocommerce/product-review-template",{},[["core/columns",{},[["core/column",{width:"40px"},[["core/avatar",{size:40,style:{border:{radius:"20px"}}}]]],["core/column",{},[["core/group",{tagName:"div",layout:{type:"flex",flexWrap:"nowrap",justifyContent:"space-between"}},[["woocommerce/product-review-author-name",{fontSize:"small"}],["woocommerce/product-review-rating"]]],["core/group",{layout:{type:"flex"},style:{spacing:{margin:{top:"0px",bottom:"0px"}}}},[["woocommerce/product-review-date",{fontSize:"small"}]]],["woocommerce/product-review-content"]]]]]]],["woocommerce/product-reviews-pagination"],["woocommerce/product-review-form"]],d={article:(0,a.__)("The <article> element should represent a self-contained, syndicatable portion of the document.","woocommerce"),aside:(0,a.__)("The <aside> element should represent a portion of a document whose content is only indirectly related to the document's main content.","woocommerce"),div:(0,a.__)("The <div> element should only be used if the block is a design element with no semantic meaning.","woocommerce"),footer:(0,a.__)("The <footer> element should represent a footer for its nearest sectioning element (e.g.: <section>, <article>, <main> etc.).","woocommerce"),header:(0,a.__)("The <header> element should represent introductory content, typically a group of introductory or navigational aids.","woocommerce"),main:(0,a.__)("The <main> element should be used for the primary content of your document only.","woocommerce"),nav:(0,a.__)("The <nav> element should be used to identify groups of links that are intended to be used for website or page content navigation.","woocommerce"),section:(0,a.__)("The <section> element should represent a standalone portion of the document that can't be better represented by another element.","woocommerce")};(e=>{const t=e.name;if(!t)return void console.error("registerProductBlockType: Block name is required for registration");const o=(({name:e,...t})=>t)(e),{isVariationBlock:r,variationName:n,isAvailableOnPostEditor:s,...c}={...o},a={blockName:t,settings:{...c},isVariationBlock:null!=r&&r,variationName:null!=n?n:void 0,isAvailableOnPostEditor:null!=s&&s};i.getInstance().registerBlockConfig(a)})({...r,edit:({attributes:e,setAttributes:o,clientId:i,context:r})=>{const{tagName:m="div"}=e,u=(0,n.useBlockProps)(),p=(0,n.useInnerBlocksProps)(u,{template:l}),{hasInvalidContext:h,warningElement:g}=(({clientId:e,postType:o,blockName:i})=>({hasInvalidContext:(0,t.useSelect)((t=>t(n.store).getBlockParentsByBlockName(e,"core/post-template").length>0&&"product"!==o),[e,o]),warningElement:(0,s.jsx)("div",{...(0,n.useBlockProps)(),children:(0,s.jsx)(n.Warning,{children:(0,a.sprintf)(/* translators: %s: block name */ /* translators: %s: block name */
(0,a.__)("The %s block requires a product context. When used in a Query Loop, the Query Loop must be configured to display products.","woocommerce"),i)})})}))({clientId:i,postType:r.postType,blockName:(0,a.__)("Product Reviews","woocommerce")});return h?g:(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)(n.InspectorControls,{group:"advanced",children:(0,s.jsx)(c.SelectControl,{__nextHasNoMarginBottom:!0,__next40pxDefaultSize:!0,label:(0,a.__)("HTML element","woocommerce"),options:[{label:(0,a.__)("Default (<div>)","woocommerce"),value:"div"},{label:"<section>",value:"section"},{label:"<aside>",value:"aside"}],value:m,onChange:e=>o({tagName:e}),help:d[m]})}),(0,s.jsx)(m,{...p})]})},save:function({attributes:{tagName:e="div"}}){const t=n.useBlockProps.save(),o=n.useInnerBlocksProps.save(t);return(0,s.jsx)(e,{...o})},deprecated:[{save:()=>null}]}),((this.wc=this.wc||{}).blocks=this.wc.blocks||{})["product-reviews"]={}})();
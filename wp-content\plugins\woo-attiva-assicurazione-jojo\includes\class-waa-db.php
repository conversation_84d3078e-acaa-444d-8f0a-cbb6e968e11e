<?php
/**
 * Classe per la gestione del database delle assicurazioni
 *
 * @package Woo_Attiva_Assicurazione
 */

// Prevengo l'accesso diretto al file
if (!defined('ABSPATH')) {
    exit;
}

/**
 * Classe per la gestione del database delle assicurazioni
 */
class WAA_DB {

    /**
     * Nome della tabella assicurazioni
     *
     * @var string
     */
    private static $table_name = 'waa_assicurazioni';

    /**
     * Restituisce il nome della tabella
     *
     * @return string Nome della tabella
     */
    public static function get_table_name() {
        return self::$table_name;
    }

    /**
     * Inizializza la classe
     */
    public static function init() {
        // Hook per aggiornamenti futuri del DB
        add_action('plugins_loaded', array(__CLASS__, 'check_version'));
    }

    /**
     * Controlla la versione del DB e aggiorna se necessario
     */
    public static function check_version() {
        if (get_option('waa_db_version') != WAA_VERSION) {
            self::create_tables();
        }
    }

    /**
     * Crea le tabelle necessarie
     */
    public static function create_tables() {
        global $wpdb;

        $table_name = $wpdb->prefix . self::$table_name;
        $charset_collate = $wpdb->get_charset_collate();

        // Controllo se la tabella esiste già
        $table_exists = $wpdb->get_var("SHOW TABLES LIKE '$table_name'");
        
        // Debug info
        error_log("WAA: Tentativo di creazione tabella $table_name");
        error_log("WAA: Tabella esiste già? " . ($table_exists ? 'Sì' : 'No'));

        $sql = "CREATE TABLE $table_name (
            id mediumint(9) NOT NULL AUTO_INCREMENT,
            order_id bigint(20) NOT NULL,
            customer_name varchar(100) NOT NULL,
            customer_email varchar(100) NOT NULL,
            product_name varchar(255) NOT NULL,
            imei varchar(50) NOT NULL,
            registration_date datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
            activation_date date NOT NULL,
            usage_date datetime DEFAULT NULL,
            usage_reason text DEFAULT NULL,
            status varchar(20) NOT NULL DEFAULT 'registrata',
            created_at datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
            updated_at datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            PRIMARY KEY  (id),
            KEY order_id (order_id),
            KEY status (status)
        ) $charset_collate;";

        // Includo il file necessario per utilizzare dbDelta
        require_once(ABSPATH . 'wp-admin/includes/upgrade.php');
        
        // Eseguo la creazione/aggiornamento della tabella
        $result = dbDelta($sql);
        
        // Debug info
        error_log("WAA: Risultato creazione tabella: " . print_r($result, true));

        // Aggiorno la versione nel database
        update_option('waa_db_version', WAA_VERSION);
    }

    /**
     * Inserisce una nuova registrazione di assicurazione
     *
     * @param array $data Dati dell'assicurazione
     * @return int|false ID dell'assicurazione inserita o false in caso di errore
     */
    public static function insert_insurance($data) {
        global $wpdb;

        $table_name = $wpdb->prefix . self::$table_name;
        
        // Preparo i dati da inserire
        $insert_data = array(
            'order_id' => $data['order_id'],
            'customer_name' => $data['customer_name'],
            'customer_email' => $data['customer_email'],
            'product_name' => $data['product_name'],
            'imei' => $data['imei'],
            'activation_date' => $data['activation_date'],
            'status' => 'registrata'
        );
        
        // Il campo registration_date ha un valore predefinito nella tabella
        
        // Debug
        error_log("WAA: Tentativo di inserimento assicurazione per ordine " . $data['order_id']);

        $result = $wpdb->insert(
            $table_name,
            $insert_data
        );

        if ($result === false) {
            error_log("WAA: Errore nell'inserimento dell'assicurazione: " . $wpdb->last_error);
            return false;
        }

        error_log("WAA: Assicurazione inserita con ID: " . $wpdb->insert_id);
        return $wpdb->insert_id;
    }

    /**
     * Aggiorna lo stato di un'assicurazione a 'utilizzata'
     *
     * @param int $insurance_id ID dell'assicurazione
     * @param string $usage_reason Motivazione dell'utilizzo
     * @param string $usage_date Data di utilizzo (opzionale)
     * @return bool Esito dell'operazione
     */
    public static function mark_insurance_as_used($insurance_id, $usage_reason, $usage_date = '') {
        global $wpdb;

        $table_name = $wpdb->prefix . self::$table_name;
        
        // Se non viene fornita una data di utilizzo, utilizzo la data corrente
        if (empty($usage_date)) {
            $usage_date = date('Y-m-d H:i:s');
        }
        
        // Debug
        error_log("WAA: Tentativo di contrassegnare l'assicurazione $insurance_id come utilizzata");

        $result = $wpdb->update(
            $table_name,
            array(
                'status' => 'utilizzata',
                'usage_date' => $usage_date,
                'usage_reason' => $usage_reason
            ),
            array('id' => $insurance_id)
        );
        
        if ($result === false) {
            error_log("WAA: Errore nell'aggiornamento dell'assicurazione: " . $wpdb->last_error);
        } else {
            error_log("WAA: Assicurazione $insurance_id contrassegnata come utilizzata");
        }

        return $result !== false;
    }

    /**
     * Ottiene le assicurazioni registrate per un dato ordine
     *
     * @param int $order_id ID dell'ordine
     * @return array Array di assicurazioni
     */
    public static function get_insurances_by_order($order_id) {
        global $wpdb;

        $table_name = $wpdb->prefix . self::$table_name;

        $results = $wpdb->get_results(
            $wpdb->prepare(
                "SELECT * FROM $table_name WHERE order_id = %d ORDER BY id DESC",
                $order_id
            ),
            ARRAY_A
        );

        return $results ?: array();
    }

    /**
     * Verifica se un prodotto ha già un'assicurazione registrata per un dato ordine
     *
     * @param int $order_id ID dell'ordine
     * @param string $product_name Nome del prodotto
     * @return bool|array Dati dell'assicurazione o false se non trovata
     */
    public static function get_insurance_by_product($order_id, $product_name) {
        global $wpdb;

        $table_name = $wpdb->prefix . self::$table_name;

        return $wpdb->get_row(
            $wpdb->prepare(
                "SELECT * FROM $table_name WHERE order_id = %d AND product_name = %s",
                $order_id,
                $product_name
            ),
            ARRAY_A
        );
    }
}

// Inizializza la classe
WAA_DB::init(); 
# Report Assicurazioni

A WordPress plugin for WooCommerce that generates comprehensive reports for orders containing insurance products from the "Assicurazioni" category.

## Description

Report Assicurazioni is a specialized WooCommerce plugin designed to help store administrators track and analyze orders that contain insurance products. The plugin provides a dedicated admin interface with filtering capabilities and PDF export functionality.

## Features

- **Admin Menu Integration**: Adds a "Report Assicurazioni" submenu under the WordPress Products menu
- **Advanced Filtering**: Filter orders by date range and search terms
- **Comprehensive Reports**: Display all orders containing products from the "Assicurazioni" category
- **Detailed Order Information**: Shows order number, date, complete product list, and total amount
- **PDF Export**: Generate PDF reports of filtered data
- **Responsive Design**: Mobile-friendly admin interface
- **Translation Ready**: Full internationalization support
- **Security**: Proper nonces, input sanitization, and validation

## Requirements

- WordPress 5.0 or higher
- WooCommerce 5.0 or higher
- PHP 7.4 or higher
- A product category named "Assicurazioni" in your WooCommerce store

## Installation

1. Upload the `report-assicurazioni` folder to the `/wp-content/plugins/` directory
2. Activate the plugin through the 'Plugins' menu in WordPress
3. Ensure WooCommerce is installed and active
4. Create a product category named "Assicurazioni" if it doesn't exist
5. Navigate to Products > Report Assicurazioni to access the plugin

## Usage

### Accessing the Report

1. Go to your WordPress admin dashboard
2. Navigate to **Products > Report Assicurazioni**
3. The report page will display all orders containing insurance products

### Filtering Reports

- **Start Date**: Filter orders from a specific date
- **End Date**: Filter orders up to a specific date
- **Search**: Search orders by order number, customer name, email, or product names
- Click "Filter Report" to apply your filters

### Exporting to PDF

1. Apply any desired filters
2. Click the "Export to PDF" button
3. The PDF will be generated and downloaded automatically

### Report Columns

- **Order Number**: Clickable link to the order details page
- **Order Date**: Formatted according to WordPress date settings
- **Products**: Complete list of all products in the order (not just insurance products)
- **Total Amount**: Order total formatted as currency

## Technical Details

### File Structure

```
report-assicurazioni/
├── report-assicurazioni.php          # Main plugin file
├── includes/
│   ├── class-admin.php               # Admin functionality
│   ├── class-report-generator.php    # Report data generation
│   └── class-pdf-exporter.php        # PDF export functionality
├── assets/
│   ├── css/
│   │   └── admin.css                 # Admin styles
│   └── js/
│       └── admin.js                  # Admin JavaScript
├── languages/
│   └── report-assicurazioni.pot      # Translation template
└── README.md                         # This file
```

### Hooks and Filters

The plugin uses standard WordPress and WooCommerce hooks:

- `plugins_loaded`: Initialize the plugin
- `admin_menu`: Add admin menu items
- `admin_enqueue_scripts`: Load admin assets
- `wp_ajax_export_report_pdf`: Handle PDF export

### Security Features

- Nonce verification for all form submissions
- Input sanitization using WordPress functions
- Capability checks (`manage_woocommerce`)
- Proper data escaping in output

### Database Usage

The plugin uses existing WooCommerce tables and doesn't create additional database tables:

- `wp_posts` (for orders and products)
- `wp_postmeta` (for order and product metadata)
- `wp_term_relationships` (for product categories)

## Customization

### Modifying the Assicurazioni Category

If your insurance category has a different name, you can modify the category detection in `includes/class-report-generator.php`:

```php
private function get_assicurazioni_category() {
    // Change 'Assicurazioni' to your category name
    $category = get_term_by('name', 'Your Category Name', 'product_cat');
    // ... rest of the method
}
```

### Adding Custom Fields

To add custom fields to the report, modify the `get_report_data()` method in `class-report-generator.php` and update the corresponding display methods in `class-admin.php`.

## Troubleshooting

### Plugin Not Showing

- Ensure WooCommerce is installed and active
- Check that you have the `manage_woocommerce` capability
- Verify the plugin is activated

### No Orders Showing

- Ensure you have a product category named "Assicurazioni"
- Check that products are properly assigned to this category
- Verify that orders exist with these products

### PDF Export Not Working

- Check server permissions for file creation
- Ensure the browser allows downloads
- Try using the HTML fallback export option

## Support

For support and bug reports, please contact the plugin developer or submit issues through the appropriate channels.

## Changelog

### Version 1.0.0
- Initial release
- Basic report functionality
- PDF export feature
- Admin interface
- Translation support

## License

This plugin is licensed under the GPL v2 or later.

## Credits

Developed for WooCommerce store administrators who need detailed insurance product reporting capabilities.

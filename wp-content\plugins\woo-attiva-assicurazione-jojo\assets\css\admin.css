/**
 * <PERSON><PERSON> per l'interfaccia di amministrazione del plugin WAA
 */

.waa-insurance-container {
    margin: 15px 0;
    font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Oxygen-Sans, Ubuntu, Cantarell, "Helvetica Neue", sans-serif;
}

.waa-insurance-table {
    width: 100%;
    border-collapse: collapse;
    margin-bottom: 20px;
    background-color: #fff;
}

.waa-insurance-table th, 
.waa-insurance-table td {
    padding: 12px;
    text-align: left;
    border: 1px solid #e2e4e7;
}

.waa-insurance-table th {
    background-color: #f8f9f9;
    font-weight: 600;
}

.waa-insurance-table tr:nth-child(even) {
    background-color: #f8f9f9;
}

.waa-form-row {
    margin-bottom: 15px;
}

.waa-form-row label {
    display: block;
    margin-bottom: 5px;
    font-weight: 600;
}

.waa-form-row input[type="text"],
.waa-form-row input[type="date"],
.waa-form-row select,
.waa-form-row textarea {
    width: 100%;
    max-width: 400px;
    padding: 8px;
    border: 1px solid #ddd;
    border-radius: 4px;
}

.waa-form-row textarea {
    min-height: 100px;
}

.waa-message {
    padding: 12px 15px;
    margin: 15px 0;
    border-radius: 4px;
    border-left: 4px solid #00a0d2;
    background-color: #f7fcff;
}

.waa-message.success {
    border-left-color: #46b450;
    background-color: #ecf7ed;
}

.waa-message.error {
    border-left-color: #dc3232;
    background-color: #fbeaea;
}

.waa-insurance-used-info {
    background-color: #f8f9f9;
    padding: 12px;
    border: 1px solid #e2e4e7;
    border-radius: 4px;
}

.waa-use-insurance-form {
    border: 1px solid #e2e4e7;
    padding: 15px;
    background-color: #fff;
    border-radius: 4px;
    margin-top: 10px;
}

/* Responsive */
@media screen and (max-width: 782px) {
    .waa-insurance-table th, 
    .waa-insurance-table td {
        padding: 8px;
    }
    
    .waa-form-row input[type="text"],
    .waa-form-row input[type="date"],
    .waa-form-row select,
    .waa-form-row textarea {
        max-width: 100%;
    }
} 
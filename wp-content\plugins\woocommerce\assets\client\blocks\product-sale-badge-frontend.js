(globalThis.webpackChunkwebpackWcBlocksFrontendJsonp=globalThis.webpackChunkwebpackWcBlocksFrontendJsonp||[]).push([[442],{3848:(e,t,o)=>{"use strict";o.r(t),o.d(t,{Block:()=>u,default:()=>d});var n=o(7723),s=o(4921),r=o(4656),l=o(2796),c=o(41),a=o(1616),i=(o(4313),o(790));const u=e=>{const{className:t,align:o,isDescendentOfSingleProductTemplate:a}=e,u=(0,c.p)(e),{parentClassName:d}=(0,l.useInnerBlockLayoutContext)(),{product:y}=(0,l.useProductDataContext)();if(!(y.id&&y.on_sale||a))return null;const f="string"==typeof o?`wc-block-components-product-sale-badge--align-${o}`:"";return(0,i.jsx)("div",{className:(0,s.A)("wc-block-components-product-sale-badge",t,f,{[`${d}__product-onsale`]:d},u.className),style:u.style,children:(0,i.jsx)(r.Label,{label:(0,n.__)("Sale","woocommerce"),screenReaderLabel:(0,n.__)("Product on sale","woocommerce")})})},d=(0,a.withProductDataContext)(u)},41:(e,t,o)=>{"use strict";o.d(t,{p:()=>i});var n=o(4921),s=o(3993),r=o(7356),l=o(9786);function c(e={}){const t={};return(0,l.getCSSRules)(e,{selector:""}).forEach((e=>{t[e.key]=e.value})),t}function a(e,t){return e&&t?`has-${(0,r.c)(t)}-${e}`:""}const i=e=>{const t=(e=>{const t=(0,s.isObject)(e)?e:{style:{}};let o=t.style;return(0,s.isString)(o)&&(o=JSON.parse(o)||{}),(0,s.isObject)(o)||(o={}),{...t,style:o}})(e),o=function(e){const{backgroundColor:t,textColor:o,gradient:r,style:l}=e,i=a("background-color",t),u=a("color",o),d=function(e){if(e)return`has-${e}-gradient-background`}(r),y=d||l?.color?.gradient;return{className:(0,n.A)(u,d,{[i]:!y&&!!i,"has-text-color":o||l?.color?.text,"has-background":t||l?.color?.background||r||l?.color?.gradient,"has-link-color":(0,s.isObject)(l?.elements?.link)?l?.elements?.link?.color:void 0}),style:c({color:l?.color||{}})}}(t),r=function(e){const t=e.style?.border||{};return{className:function(e){const{borderColor:t,style:o}=e,s=t?a("border-color",t):"";return(0,n.A)({"has-border-color":!!t||!!o?.border?.color,[s]:!!s})}(e),style:c({border:t})}}(t),l=function(e){return{className:void 0,style:c({spacing:e.style?.spacing||{}})}}(t),i=(e=>{const t=(0,s.isObject)(e.style.typography)?e.style.typography:{},o=(0,s.isString)(t.fontFamily)?t.fontFamily:"";return{className:e.fontFamily?`has-${e.fontFamily}-font-family`:o,style:{fontSize:e.fontSize?`var(--wp--preset--font-size--${e.fontSize})`:t.fontSize,fontStyle:t.fontStyle,fontWeight:t.fontWeight,letterSpacing:t.letterSpacing,lineHeight:t.lineHeight,textDecoration:t.textDecoration,textTransform:t.textTransform}}})(t);return{className:(0,n.A)(i.className,o.className,r.className,l.className),style:{...i.style,...o.style,...r.style,...l.style}}}},4313:()=>{}}]);
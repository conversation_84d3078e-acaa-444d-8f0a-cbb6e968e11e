(()=>{var e,t,o,r={1635:(e,t,o)=>{"use strict";const r=window.wp.blocks;var s=o(4530),i=o(3791);const n=window.wp.blockEditor;var a=o(7723);const c=window.wp.components;var l=o(4921),d=o(6087);function m(e,t,o){const r=new Set(t.map((e=>e[o])));return e.filter((e=>!r.has(e[o])))}const w=window.wp.htmlEntities;var h=o(790);const u={clear:(0,a.__)("Clear all selected items","woocommerce"),noItems:(0,a.__)("No items found.","woocommerce"),
/* Translators: %s search term */
noResults:(0,a.__)("No results for %s","woocommerce"),search:(0,a.__)("Search for items","woocommerce"),selected:e=>(0,a.sprintf)(/* translators: Number of items selected from list. */ /* translators: Number of items selected from list. */
(0,a._n)("%d item selected","%d items selected",e,"woocommerce"),e),updated:(0,a.__)("Search results updated.","woocommerce")},p=(e,t=e)=>{const o=e.reduce(((e,t)=>{const o=t.parent||0;return e[o]||(e[o]=[]),e[o].push(t),e}),{}),r=t.reduce(((e,t)=>(e[String(t.id)]=t,e)),{});const s=["0"],i=(e={})=>e.parent?[...i(r[e.parent]),e.name]:e.name?[e.name]:[],n=e=>e.map((e=>{const t=o[e.id];return s.push(""+e.id),{...e,breadcrumbs:i(r[e.parent]),children:t&&t.length?n(t):[]}})),a=n(o[0]||[]);return Object.entries(o).forEach((([e,t])=>{s.includes(e)||a.push(...n(t||[]))})),a},g=(e,t)=>{if(!t)return e;const o=new RegExp(`(${t.replace(/[-\/\\^$*+?.()|[\]{}]/g,"\\$&")})`,"ig");return e.split(o).map(((e,t)=>o.test(e)?(0,h.jsx)("strong",{children:e},t):(0,h.jsx)(d.Fragment,{children:e},t)))},v=({label:e})=>(0,h.jsx)("span",{className:"woocommerce-search-list__item-count",children:e}),_=e=>{const{item:t,search:o}=e,r=t.breadcrumbs&&t.breadcrumbs.length;return(0,h.jsxs)("span",{className:"woocommerce-search-list__item-label",children:[r?(0,h.jsx)("span",{className:"woocommerce-search-list__item-prefix",children:(s=t.breadcrumbs,1===s.length?s.slice(0,1).toString():2===s.length?s.slice(0,1).toString()+" › "+s.slice(-1).toString():s.slice(0,1).toString()+" … "+s.slice(-1).toString())}):null,(0,h.jsx)("span",{className:"woocommerce-search-list__item-name",children:g((0,w.decodeEntities)(t.name),o)})]});var s},b=({countLabel:e,className:t,depth:o=0,controlId:r="",item:s,isSelected:i,isSingle:n,onSelect:a,search:u="",selected:p,useExpandedPanelId:b,...x})=>{const[y,f]=b,j=null!=e&&void 0!==s.count&&null!==s.count,R=!!s.breadcrumbs?.length,k=!!s.children?.length,S=y===s.id,C=(0,l.A)(["woocommerce-search-list__item",`depth-${o}`,t],{"has-breadcrumbs":R,"has-children":k,"has-count":j,"is-expanded":S,"is-radio-button":n});(0,d.useEffect)((()=>{k&&i&&f(s.id)}),[s,k,i,f]);const N=x.name||`search-list-item-${r}`,T=`${N}-${s.id}`,O=(0,d.useCallback)((()=>{f(S?-1:Number(s.id))}),[S,s.id,f]);return k?(0,h.jsx)("div",{className:C,onClick:O,onKeyDown:e=>"Enter"===e.key||" "===e.key?O():null,role:"treeitem",tabIndex:0,children:n?(0,h.jsxs)(h.Fragment,{children:[(0,h.jsx)("input",{type:"radio",id:T,name:N,value:s.value,onChange:a(s),onClick:e=>e.stopPropagation(),checked:i,className:"woocommerce-search-list__item-input",...x}),(0,h.jsx)(_,{item:s,search:u}),j?(0,h.jsx)(v,{label:e||s.count}):null]}):(0,h.jsxs)(h.Fragment,{children:[(0,h.jsx)(c.CheckboxControl,{className:"woocommerce-search-list__item-input",checked:i,...!i&&s.children.some((e=>p.find((t=>t.id===e.id))))?{indeterminate:!0}:{},label:g((0,w.decodeEntities)(s.name),u),onChange:()=>{i?a(m(p,s.children,"id"))():a(function(e,t){const o=m(t,e,"id");return[...e,...o]}(p,s.children))()},onClick:e=>e.stopPropagation()}),j?(0,h.jsx)(v,{label:e||s.count}):null]})}):(0,h.jsxs)("label",{htmlFor:T,className:C,children:[n?(0,h.jsxs)(h.Fragment,{children:[(0,h.jsx)("input",{...x,type:"radio",id:T,name:N,value:s.value,onChange:a(s),checked:i,className:"woocommerce-search-list__item-input"}),(0,h.jsx)(_,{item:s,search:u})]}):(0,h.jsx)(c.CheckboxControl,{...x,id:T,name:N,className:"woocommerce-search-list__item-input",value:(0,w.decodeEntities)(s.value),label:g((0,w.decodeEntities)(s.name),u),onChange:a(s),checked:i}),j?(0,h.jsx)(v,{label:e||s.count}):null]})},x=b;var y=o(2624),f=o(9491),j=o(3028);o(5022);const R=({id:e,label:t,popoverContents:o,remove:r,screenReaderLabel:i,className:n=""})=>{const[m,u]=(0,d.useState)(!1),p=(0,f.useInstanceId)(R);if(i=i||t,!t)return null;t=(0,w.decodeEntities)(t);const g=(0,l.A)("woocommerce-tag",n,{"has-remove":!!r}),v=`woocommerce-tag__label-${p}`,_=(0,h.jsxs)(h.Fragment,{children:[(0,h.jsx)("span",{className:"screen-reader-text",children:i}),(0,h.jsx)("span",{"aria-hidden":"true",children:t})]});return(0,h.jsxs)("span",{className:g,children:[o?(0,h.jsx)(c.Button,{className:"woocommerce-tag__text",id:v,onClick:()=>u(!0),children:_}):(0,h.jsx)("span",{className:"woocommerce-tag__text",id:v,children:_}),o&&m&&(0,h.jsx)(c.Popover,{onClose:()=>u(!1),children:o}),r&&(0,h.jsx)(c.Button,{className:"woocommerce-tag__remove",onClick:r(e),label:(0,a.sprintf)(
// Translators: %s label.
// Translators: %s label.
(0,a.__)("Remove %s","woocommerce"),t),"aria-describedby":v,children:(0,h.jsx)(s.A,{icon:j.A,size:20,className:"clear-icon",role:"img"})})]})},k=R;o(1939);const S=e=>(0,h.jsx)(x,{...e}),C=e=>{const{list:t,selected:o,renderItem:r,depth:s=0,onSelect:i,instanceId:n,isSingle:a,search:c,useExpandedPanelId:l}=e,[m]=l;return t?(0,h.jsx)(h.Fragment,{children:t.map((t=>{const w=t.children?.length&&!a?t.children.every((({id:e})=>o.find((t=>t.id===e)))):!!o.find((({id:e})=>e===t.id)),u=t.children?.length&&m===t.id;return(0,h.jsxs)(d.Fragment,{children:[(0,h.jsx)("li",{children:r({item:t,isSelected:w,onSelect:i,isSingle:a,selected:o,search:c,depth:s,useExpandedPanelId:l,controlId:n})}),u?(0,h.jsx)(C,{...e,list:t.children,depth:s+1}):null]},t.id)}))}):null},N=({isLoading:e,isSingle:t,selected:o,messages:r,onChange:s,onRemove:i})=>{if(e||t||!o)return null;const n=o.length;return(0,h.jsxs)("div",{className:"woocommerce-search-list__selected",children:[(0,h.jsxs)("div",{className:"woocommerce-search-list__selected-header",children:[(0,h.jsx)("strong",{children:r.selected(n)}),n>0?(0,h.jsx)(c.Button,{variant:"link",isDestructive:!0,onClick:()=>s([]),"aria-label":r.clear,children:(0,a.__)("Clear all","woocommerce")}):null]}),n>0?(0,h.jsx)("ul",{children:o.map(((e,t)=>(0,h.jsx)("li",{children:(0,h.jsx)(k,{label:e.name,id:e.id,remove:i})},t)))}):null]})},T=({filteredList:e,search:t,onSelect:o,instanceId:r,useExpandedPanelId:i,...n})=>{const{messages:c,renderItem:l,selected:d,isSingle:m}=n,w=l||S;return 0===e.length?(0,h.jsxs)("div",{className:"woocommerce-search-list__list is-not-found",children:[(0,h.jsx)("span",{className:"woocommerce-search-list__not-found-icon",children:(0,h.jsx)(s.A,{icon:y.A,role:"img"})}),(0,h.jsx)("span",{className:"woocommerce-search-list__not-found-text",children:t?(0,a.sprintf)(c.noResults,t):c.noItems})]}):(0,h.jsx)("ul",{className:"woocommerce-search-list__list",children:(0,h.jsx)(C,{useExpandedPanelId:i,list:e,selected:d,renderItem:w,onSelect:o,instanceId:r,isSingle:m,search:t})})},O=e=>{const{className:t="",isCompact:o,isHierarchical:r,isLoading:s,isSingle:i,list:n,messages:m=u,onChange:w,onSearch:g,selected:v,type:_="text",debouncedSpeak:b}=e,[x,y]=(0,d.useState)(""),j=(0,d.useState)(-1),R=(0,f.useInstanceId)(O),k=(0,d.useMemo)((()=>({...u,...m})),[m]),S=(0,d.useMemo)((()=>((e,t,o)=>{if(!t)return o?p(e):e;const r=new RegExp(t.replace(/[-\/\\^$*+?.()|[\]{}]/g,"\\$&"),"i"),s=e.map((e=>!!r.test(e.name)&&e)).filter(Boolean);return o?p(s,e):s})(n,x,r)),[n,x,r]);(0,d.useEffect)((()=>{b&&b(k.updated)}),[b,k]),(0,d.useEffect)((()=>{"function"==typeof g&&g(x)}),[x,g]);const C=(0,d.useCallback)((e=>()=>{i&&w([]);const t=v.findIndex((({id:t})=>t===e));w([...v.slice(0,t),...v.slice(t+1)])}),[i,v,w]),E=(0,d.useCallback)((e=>()=>{Array.isArray(e)?w(e):-1===v.findIndex((({id:t})=>t===e.id))?w(i?[e]:[...v,e]):C(e.id)()}),[i,C,w,v]),L=(0,d.useCallback)((e=>{const[t]=v.filter((t=>!e.find((e=>t.id===e.id))));C(t.id)()}),[C,v]);return(0,h.jsxs)("div",{className:(0,l.A)("woocommerce-search-list",t,{"is-compact":o,"is-loading":s,"is-token":"token"===_}),children:["text"===_&&(0,h.jsx)(N,{...e,onRemove:C,messages:k}),(0,h.jsx)("div",{className:"woocommerce-search-list__search",children:"text"===_?(0,h.jsx)(c.TextControl,{label:k.search,type:"search",value:x,onChange:e=>y(e)}):(0,h.jsx)(c.FormTokenField,{disabled:s,label:k.search,onChange:L,onInputChange:e=>y(e),suggestions:[],__experimentalValidateInput:()=>!1,value:s?[(0,a.__)("Loading…","woocommerce")]:v.map((e=>({...e,value:e.name}))),__experimentalShowHowTo:!1})}),s?(0,h.jsx)("div",{className:"woocommerce-search-list__list",children:(0,h.jsx)(c.Spinner,{})}):(0,h.jsx)(T,{...e,search:x,filteredList:S,messages:k,onSelect:E,instanceId:R,useExpandedPanelId:j})]})},E=((0,c.withSpokenMessages)(O),window.wp.url),L=window.wp.apiFetch;var A=o.n(L);const P=window.wc.wcSettings,I=(0,P.getSetting)("wcBlocksConfig",{pluginUrl:"",productCount:0,defaultAvatar:"",restApiRoutes:{},wordCountType:"words"}),M=(I.pluginUrl,I.pluginUrl,P.STORE_PAGES.shop,P.STORE_PAGES.checkout,P.STORE_PAGES.checkout,P.STORE_PAGES.privacy,P.STORE_PAGES.privacy,P.STORE_PAGES.terms,P.STORE_PAGES.terms,P.STORE_PAGES.cart,P.STORE_PAGES.cart,P.STORE_PAGES.myaccount?.permalink?P.STORE_PAGES.myaccount.permalink:(0,P.getSetting)("wpLoginUrl","/wp-login.php"),(0,P.getSetting)("localPickupEnabled",!1),(0,P.getSetting)("shippingMethodsExist",!1),(0,P.getSetting)("shippingEnabled",!0),(0,P.getSetting)("countries",{})),D=(0,P.getSetting)("countryData",{}),F={...Object.fromEntries(Object.keys(D).filter((e=>!0===D[e].allowBilling)).map((e=>[e,M[e]||""]))),...Object.fromEntries(Object.keys(D).filter((e=>!0===D[e].allowShipping)).map((e=>[e,M[e]||""])))},$=(Object.fromEntries(Object.keys(F).map((e=>[e,D[e].states||{}]))),Object.fromEntries(Object.keys(F).map((e=>[e,D[e].locale||{}]))),{address:["first_name","last_name","company","address_1","address_2","city","postcode","country","state","phone"],contact:["email"],order:[]}),B=((0,P.getSetting)("addressFieldsLocations",$).address,(0,P.getSetting)("addressFieldsLocations",$).contact,(0,P.getSetting)("addressFieldsLocations",$).order,(0,P.getSetting)("additionalOrderFields",{}),(0,P.getSetting)("additionalContactFields",{}),(0,P.getSetting)("additionalAddressFields",{}),async e=>{if(!("json"in e))return{code:e.code||"",message:e.message,type:e.type||"general"};try{const t=await e.json();return{code:t.code||"",message:t.message,type:t.type||"api"}}catch(e){return{message:e.message,type:"general"}}}),H=window.wp.escapeHtml,G=({message:e,type:t})=>e?"general"===t?(0,h.jsxs)("span",{children:[(0,a.__)("The following error was returned","woocommerce"),(0,h.jsx)("br",{}),(0,h.jsx)("code",{children:(0,H.escapeHTML)(e)})]}):"api"===t?(0,h.jsxs)("span",{children:[(0,a.__)("The following error was returned from the API","woocommerce"),(0,h.jsx)("br",{}),(0,h.jsx)("code",{children:(0,H.escapeHTML)(e)})]}):e:(0,a.__)("An error has prevented the block from being updated.","woocommerce"),W=({error:e})=>(0,h.jsx)("div",{className:"wc-block-error-message",children:G(e)}),J=e=>{const{id:t,name:o,parent:r,count:s}=e;return{id:t,name:o,parent:r,count:s,breadcrumbs:[],children:[],details:e,value:e.slug}};o(6982);const U=(z=({categories:e=[],error:t=null,isLoading:o=!1,onChange:r,onOperatorChange:s,operator:i="any",selected:n,isCompact:d=!1,isSingle:m=!1,showReviewCount:w})=>{const u={clear:(0,a.__)("Clear all product categories","woocommerce"),list:(0,a.__)("Product Categories","woocommerce"),noItems:(0,a.__)("Your store doesn't have any product categories.","woocommerce"),search:(0,a.__)("Search for product categories","woocommerce"),selected:e=>(0,a.sprintf)(/* translators: %d is the count of selected categories. */ /* translators: %d is the count of selected categories. */
(0,a._n)("%d category selected","%d categories selected",e,"woocommerce"),e),updated:(0,a.__)("Category search results updated.","woocommerce")};if(t)return(0,h.jsx)(W,{error:t});const p=e.map(J);return(0,h.jsxs)(h.Fragment,{children:[(0,h.jsx)(O,{className:"woocommerce-product-categories",list:p,isLoading:o,selected:p.filter((({id:e})=>n.includes(Number(e)))),onChange:r,renderItem:e=>{const{item:t,search:o,depth:r=0}=e,s=t.breadcrumbs.length?`${t.breadcrumbs.join(", ")}, ${t.name}`:t.name,i=w?(0,a.sprintf)(/* translators: %1$s is the item name, %2$d is the count of reviews for the item. */ /* translators: %1$s is the item name, %2$d is the count of reviews for the item. */
(0,a._n)("%1$s, has %2$d review","%1$s, has %2$d reviews",t.details?.review_count||0,"woocommerce"),s,t.details?.review_count||0):(0,a.sprintf)(/* translators: %1$s is the item name, %2$d is the count of products for the item. */ /* translators: %1$s is the item name, %2$d is the count of products for the item. */
(0,a._n)("%1$s, has %2$d product","%1$s, has %2$d products",t.details?.count||0,"woocommerce"),s,t.details?.count||0),n=w?(0,a.sprintf)(/* translators: %d is the count of reviews. */ /* translators: %d is the count of reviews. */
(0,a._n)("%d review","%d reviews",t.details?.review_count||0,"woocommerce"),t.details?.review_count||0):(0,a.sprintf)(/* translators: %d is the count of products. */ /* translators: %d is the count of products. */
(0,a._n)("%d product","%d products",t.details?.count||0,"woocommerce"),t.details?.count||0);return(0,h.jsx)(b,{className:(0,l.A)("woocommerce-product-categories__item","has-count",{"is-searching":o.length>0,"is-skip-level":0===r&&0!==t.parent}),...e,countLabel:n,"aria-label":i})},messages:u,isCompact:d,isHierarchical:!0,isSingle:m}),!!s&&(0,h.jsx)("div",{hidden:n.length<2,children:(0,h.jsx)(c.SelectControl,{className:"woocommerce-product-categories__operator",label:(0,a.__)("Display products matching","woocommerce"),help:(0,a.__)("Pick at least two categories to use this setting.","woocommerce"),value:i,onChange:s,options:[{label:(0,a.__)("Any selected categories","woocommerce"),value:"any"},{label:(0,a.__)("All selected categories","woocommerce"),value:"all"}]})})]})},({selected:e,...t})=>{const[o,r]=(0,d.useState)(!0),[s,i]=(0,d.useState)(null),[n,a]=(0,d.useState)([]),c=async e=>{const t=await B(e);i(t),r(!1)},l=(0,d.useRef)(e);return(0,d.useEffect)((()=>{var e;(e={selected:l.current},A()({path:(0,E.addQueryArgs)("wc/store/v1/products/categories",{per_page:0,...e})})).then((e=>{a(e),r(!1)})).catch(c)}),[l]),(0,h.jsx)(z,{...t,selected:e,error:s,categories:n,isLoading:o})});var z,V=o(2098);o(3120);const q=({className:e="",error:t,isLoading:o=!1,onRetry:r})=>(0,h.jsxs)(c.Placeholder,{icon:(0,h.jsx)(s.A,{icon:V.A}),label:(0,a.__)("Sorry, an error occurred","woocommerce"),className:(0,l.A)("wc-block-api-error",e),children:[(0,h.jsx)(W,{error:t}),r&&(0,h.jsx)(h.Fragment,{children:o?(0,h.jsx)(c.Spinner,{}):(0,h.jsx)(c.Button,{variant:"secondary",onClick:r,children:(0,a.__)("Retry","woocommerce")})})]});o(6919);var K=o(3240),Q=o.n(K);const X=["a","b","em","i","strong","p","br"],Y=["target","href","rel","name","download"],Z=(e,t)=>{const o=t?.tags||X,r=t?.attr||Y;return Q().sanitize(e,{ALLOWED_TAGS:o,ALLOWED_ATTR:r})},ee=({label:e,screenReaderLabel:t,wrapperElement:o,wrapperProps:r={},allowHTML:s=!1})=>{let i;const n=null!=e,a=null!=t;return!n&&a?(i=o||"span",r={...r,className:(0,l.A)(r.className,"screen-reader-text")},(0,h.jsx)(i,{...r,children:t})):(i=o||d.Fragment,n&&a&&e!==t?(0,h.jsxs)(i,{...r,children:[s?(0,h.jsx)(d.RawHTML,{children:Z(e,{tags:["b","em","i","strong","p","br","span"],attr:["style"]})}):(0,h.jsx)("span",{"aria-hidden":"true",children:e}),(0,h.jsx)("span",{className:"screen-reader-text",children:t})]}):(0,h.jsx)(i,{...r,children:e}))},te=({onClick:e,label:t=(0,a.__)("Load more","woocommerce"),screenReaderLabel:o=(0,a.__)("Load more","woocommerce")})=>(0,h.jsx)("div",{className:"wp-block-button wc-block-load-more wc-block-components-load-more",children:(0,h.jsx)("button",{className:"wp-block-button__link",onClick:e,children:(0,h.jsx)(ee,{label:t,screenReaderLabel:o})})}),oe=window.wc.blocksComponents;o(6878);const re=({onChange:e,readOnly:t,value:o})=>(0,h.jsx)(oe.SortSelect,{className:"wc-block-review-sort-select wc-block-components-review-sort-select",label:(0,a.__)("Order by","woocommerce"),onChange:e,options:[{key:"most-recent",label:(0,a.__)("Most recent","woocommerce")},{key:"highest-rating",label:(0,a.__)("Highest rating","woocommerce")},{key:"lowest-rating",label:(0,a.__)("Lowest rating","woocommerce")}],readOnly:t,screenReaderLabel:(0,a.__)("Order reviews by","woocommerce"),value:o});function se(e){let t,o,r,s=[];for(let i=0;i<e.length;i++)t=e.substring(i),o=t.match(/^&[a-z0-9#]+;/),o?(r=o[0],s.push(r),i+=r.length-1):s.push(e[i]);return s}const ie=(e,t,o="...")=>{const r=function(e,t){const o=(t=t||{}).limit||100,r=void 0===t.preserveTags||t.preserveTags,s=void 0!==t.wordBreak&&t.wordBreak,i=t.suffix||"...",n=t.moreLink||"",a=t.moreText||"»",c=t.preserveWhiteSpace||!1,l=e.replace(/</g,"\n<").replace(/>/g,">\n").replace(/\n\n/g,"\n").replace(/^\n/g,"").replace(/\n$/g,"").split("\n");let d,m,w,h,u,p,g=0,v=[],_=!1;for(let e=0;e<l.length;e++){if(d=l[e],h=c?d:d.replace(/[ ]+/g," "),!d.length)continue;const t=se(h);if("<"!==d[0])if(g>=o)d="";else if(g+t.length>=o){if(m=o-g," "===t[m-1])for(;m&&(m-=1," "===t[m-1]););else w=t.slice(m).indexOf(" "),s||(-1!==w?m+=w:m=d.length);if(d=t.slice(0,m).join("")+i,n){const e=document.createElement("a");e.href=n,e.style.display="inline",e.textContent=a,d+=e.outerHTML}g=o,_=!0}else g+=t.length;else if(r){if(g>=o)if(u=d.match(/[a-zA-Z]+/),p=u?u[0]:"",p)if("</"!==d.substring(0,2))v.push(p),d="";else{for(;v[v.length-1]!==p&&v.length;)v.pop();v.length&&(d=""),v.pop()}else d=""}else d="";l[e]=d}return{html:l.join("\n").replace(/\n/g,""),more:_}}(e,{suffix:o,limit:t});return r.html},ne=(e,t,o)=>(t<=o?e.start=e.middle+1:e.end=e.middle-1,e),ae=(e,t,o,r)=>{const s=((e,t,o)=>{let r={start:0,middle:0,end:e.length};for(;r.start<=r.end;)r.middle=Math.floor((r.start+r.end)/2),t.innerHTML=ie(e,r.middle),r=ne(r,t.clientHeight,o);return r.middle})(e,t,o);return ie(e,s-r.length,r)},ce={className:"read-more-content",ellipsis:"&hellip;",lessText:(0,a.__)("Read less","woocommerce"),maxLines:3,moreText:(0,a.__)("Read more","woocommerce")};class le extends d.Component{static defaultProps=ce;constructor(e){super(e),this.state={isExpanded:!1,clampEnabled:null,content:e.children,summary:"."},this.reviewContent=(0,d.createRef)(),this.reviewSummary=(0,d.createRef)(),this.getButton=this.getButton.bind(this),this.onClick=this.onClick.bind(this)}componentDidMount(){this.setSummary()}componentDidUpdate(e){e.maxLines===this.props.maxLines&&e.children===this.props.children||this.setState({clampEnabled:null,summary:"."},this.setSummary)}setSummary(){if(this.props.children){const{maxLines:e,ellipsis:t}=this.props;if(!this.reviewSummary.current||!this.reviewContent.current)return;const o=(this.reviewSummary.current.clientHeight+1)*e+1,r=this.reviewContent.current.clientHeight+1>o;this.setState({clampEnabled:r}),r&&this.setState({summary:ae(this.reviewContent.current.innerHTML,this.reviewSummary.current,o,t)})}}getButton(){const{isExpanded:e}=this.state,{className:t,lessText:o,moreText:r}=this.props,s=e?o:r;if(s)return(0,h.jsx)("a",{href:"#more",className:t+"__read_more",onClick:this.onClick,"aria-expanded":!e,role:"button",children:s})}onClick(e){e.preventDefault();const{isExpanded:t}=this.state;this.setState({isExpanded:!t})}render(){const{className:e}=this.props,{content:t,summary:o,clampEnabled:r,isExpanded:s}=this.state;return t?!1===r?(0,h.jsx)("div",{className:e,children:(0,h.jsx)("div",{ref:this.reviewContent,children:t})}):(0,h.jsxs)("div",{className:e,children:[(!s||null===r)&&(0,h.jsx)("div",{ref:this.reviewSummary,"aria-hidden":s,dangerouslySetInnerHTML:{__html:o}}),(s||null===r)&&(0,h.jsx)("div",{ref:this.reviewContent,"aria-hidden":!s,children:t}),this.getButton()]}):null}}const de=le;function me(e,t,o){return o||!e?(0,h.jsx)("div",{className:"wc-block-review-list-item__image wc-block-components-review-list-item__image"}):(0,h.jsxs)("div",{className:"wc-block-review-list-item__image wc-block-components-review-list-item__image",children:["product"===t?(0,h.jsx)("img",{"aria-hidden":"true",alt:e.product_image?.alt||"",src:e.product_image?.thumbnail||""}):(0,h.jsx)("img",{"aria-hidden":"true",alt:"",src:e.reviewer_avatar_urls[96]||""}),e.verified&&(0,h.jsx)("div",{className:"wc-block-review-list-item__verified wc-block-components-review-list-item__verified",title:(0,a.__)("Verified buyer","woocommerce"),children:(0,a.__)("Verified buyer","woocommerce")})]})}function we(e){return(0,h.jsx)(de,{maxLines:10,moreText:(0,a.__)("Read full review","woocommerce"),lessText:(0,a.__)("Hide full review","woocommerce"),className:"wc-block-review-list-item__text wc-block-components-review-list-item__text",children:(0,h.jsx)("div",{dangerouslySetInnerHTML:{__html:e.review||""}})})}function he(e,t){return(0,h.jsx)("div",{className:"wc-block-review-list-item__product wc-block-components-review-list-item__product",children:(0,h.jsx)("a",{href:e.product_permalink,"aria-labelledby":t,children:(0,w.decodeEntities)(e.product_name)})})}function ue(e){const{reviewer:t=""}=e;return(0,h.jsx)("div",{className:"wc-block-review-list-item__author wc-block-components-review-list-item__author",children:t})}function pe(e){const{date_created:t,formatted_date_created:o}=e;return(0,h.jsx)("time",{className:"wc-block-review-list-item__published-date wc-block-components-review-list-item__published-date",dateTime:t,children:o})}function ge(e,t){const{rating:o}=e,r={width:o/5*100+"%"},s=(0,a.sprintf)(/* translators: %f is referring to the average rating value */ /* translators: %f is referring to the average rating value */
(0,a.__)("Rated %f out of 5","woocommerce"),o),i={__html:(0,a.sprintf)(/* translators: %s is referring to the average rating value */ /* translators: %s is referring to the average rating value */
(0,a.__)("Rated %s out of 5","woocommerce"),(0,a.sprintf)('<strong class="rating">%f</strong>',o))};return(0,h.jsx)("div",{id:t,"aria-label":`${(0,w.decodeEntities)(e.product_name)} ${s}`,className:"wc-block-review-list-item__rating wc-block-components-review-list-item__rating",children:(0,h.jsx)("div",{"aria-hidden":"true",className:`wc-block-review-list-item__rating__stars wc-block-components-review-list-item__rating__stars wc-block-review-list-item__rating__stars--${o}`,role:"img",children:(0,h.jsx)("span",{style:r,dangerouslySetInnerHTML:i})})})}o(7313);const ve=({attributes:e,review:t={}})=>{const{imageType:o,showReviewDate:r,showReviewerName:s,showReviewImage:i,showReviewRating:n,showReviewContent:a,showProductName:c}=e,{rating:m}=t,w=!(Object.keys(t).length>0),u=Number.isFinite(m)&&n,p=(0,d.useId)();return(0,h.jsxs)("li",{className:(0,l.A)("wc-block-review-list-item__item","wc-block-components-review-list-item__item",{"is-loading":w,"wc-block-components-review-list-item__item--has-image":i}),"aria-hidden":w,children:[(c||r||s||i||u)&&(0,h.jsxs)("div",{className:"wc-block-review-list-item__info wc-block-components-review-list-item__info",children:[i&&me(t,o,w),(c||s||u||r)&&(0,h.jsxs)("div",{className:"wc-block-review-list-item__meta wc-block-components-review-list-item__meta",children:[u&&ge(t,p),c&&he(t,p),s&&ue(t),r&&pe(t)]})]}),a&&we(t)]})};o(5183);const _e=({attributes:e,reviews:t})=>{const o=(0,P.getSetting)("showAvatars",!0),r=(0,P.getSetting)("reviewRatingsEnabled",!0),s=(o||"product"===e.imageType)&&e.showReviewImage,i=r&&e.showReviewRating,n={...e,showReviewImage:s,showReviewRating:i};return(0,h.jsx)("ul",{className:"wc-block-review-list wc-block-components-review-list",children:0===t.length?(0,h.jsx)(ve,{attributes:n}):t.map(((e,t)=>(0,h.jsx)(ve,{attributes:n,review:e},e.id||t)))})};var be=o(923),xe=o.n(be);const ye=e=>{const{className:t,categoryIds:o,productId:r,showReviewDate:s,showReviewerName:i,showReviewContent:n,showProductName:a,showReviewImage:c,showReviewRating:d}=e;let m="wc-block-all-reviews";return r&&(m="wc-block-reviews-by-product"),Array.isArray(o)&&(m="wc-block-reviews-by-category"),(0,l.A)(m,t,{"has-image":c,"has-name":i,"has-date":s,"has-rating":d,"has-content":n,"has-product-name":a})},fe=e=>{const{categoryIds:t,imageType:o,orderby:r,productId:s,reviewsOnPageLoad:i,reviewsOnLoadMore:n,showLoadMore:a,showOrderby:c}=e,l={"data-image-type":o,"data-orderby":r,"data-reviews-on-page-load":i,"data-reviews-on-load-more":n,"data-show-load-more":a,"data-show-orderby":c};return s&&(l["data-product-id"]=s),Array.isArray(t)&&(l["data-category-ids"]=t.join(",")),l};class je extends d.Component{render(){const{attributes:e,error:t,isLoading:o,noReviewsPlaceholder:r,reviews:s,totalReviews:i}=this.props;if(t)return(0,h.jsx)(q,{className:"wc-block-featured-product-error",error:t,isLoading:o});if(0===s.length&&!o)return(0,h.jsx)(r,{attributes:e});const n=(0,P.getSetting)("reviewRatingsEnabled",!0);return(0,h.jsxs)(c.Disabled,{children:[e.showOrderby&&n&&(0,h.jsx)(re,{readOnly:!0,value:e.orderby,onChange:()=>null}),(0,h.jsx)(_e,{attributes:e,reviews:s}),e.showLoadMore&&i>s.length&&(0,h.jsx)(te,{screenReaderLabel:(0,a.__)("Load more reviews","woocommerce"),onClick:()=>null})]})}}const Re=(e=>{var t;class o extends d.Component{isPreview=!!this.props.attributes.previewReviews;delayedAppendReviews=(null!==(t=this.props.delayFunction)&&void 0!==t?t:e=>e)(this.appendReviews);isMounted=!1;state={error:null,loading:!0,reviews:this.isPreview&&this.props.attributes?.previewReviews?this.props.attributes.previewReviews:[],totalReviews:this.isPreview&&this.props.attributes?.previewReviews?this.props.attributes.previewReviews.length:0};componentDidMount(){this.isMounted=!0,this.replaceReviews()}componentDidUpdate(e){e.reviewsToDisplay<this.props.reviewsToDisplay?this.delayedAppendReviews():this.shouldReplaceReviews(e,this.props)&&this.replaceReviews()}shouldReplaceReviews(e,t){return e.orderby!==t.orderby||e.order!==t.order||e.productId!==t.productId||!xe()(e.categoryIds,t.categoryIds)}componentWillUnmount(){this.isMounted=!1,"cancel"in this.delayedAppendReviews&&"function"==typeof this.delayedAppendReviews.cancel&&this.delayedAppendReviews.cancel()}getArgs(e){const{categoryIds:t,order:o,orderby:r,productId:s,reviewsToDisplay:i}=this.props,n={order:o,orderby:r,per_page:i-e,offset:e};if(t){const e=Array.isArray(t)?t:JSON.parse(t);n.category_id=Array.isArray(e)?e.join(","):e}return s&&(n.product_id=s),n}replaceReviews(){var e;if(this.isPreview)return;const t=null!==(e=this.props.onReviewsReplaced)&&void 0!==e?e:()=>{};this.updateListOfReviews().then(t)}appendReviews(){var e;if(this.isPreview)return;const t=null!==(e=this.props.onReviewsAppended)&&void 0!==e?e:()=>{},{reviewsToDisplay:o}=this.props,{reviews:r}=this.state;o<=r.length||this.updateListOfReviews(r).then(t)}updateListOfReviews(e=[]){const{reviewsToDisplay:t}=this.props,{totalReviews:o}=this.state,r=Math.min(o,t)-e.length;return this.setState({loading:!0,reviews:e.concat(Array(r).fill({}))}),(s=this.getArgs(e.length),A()({path:"/wc/store/v1/products/reviews?"+Object.entries(s).map((e=>e.join("="))).join("&"),parse:!1}).then((e=>e.json().then((t=>({reviews:t,totalReviews:parseInt(e.headers.get("x-wp-total"),10)})))))).then((({reviews:t,totalReviews:o})=>(this.isMounted&&this.setState({reviews:e.filter((e=>Object.keys(e).length)).concat(t),totalReviews:o,loading:!1,error:null}),{newReviews:t}))).catch(this.setError);var s}setError=async e=>{var t;if(!this.isMounted)return;const o=null!==(t=this.props.onReviewsLoadError)&&void 0!==t?t:()=>{},r=await B(e);this.setState({reviews:[],loading:!1,error:r}),o(r)};render(){const{reviewsToDisplay:t}=this.props,{error:o,loading:r,reviews:s,totalReviews:i}=this.state;return(0,h.jsx)(e,{...this.props,error:o,isLoading:r,reviews:s.slice(0,t),totalReviews:i})}}const{displayName:r=e.name||"Component"}=e;return o.displayName=`WithReviews(${r})`,o})(je),ke=({attributes:e,icon:t,name:o,noReviewsPlaceholder:r})=>{const{categoryIds:s,productId:i,reviewsOnPageLoad:n,showProductName:l,showReviewDate:d,showReviewerName:m,showReviewContent:w,showReviewImage:u,showReviewRating:p}=e,{order:g,orderby:v}=(e=>{if((0,P.getSetting)("reviewRatingsEnabled",!0)){if("lowest-rating"===e)return{order:"asc",orderby:"rating"};if("highest-rating"===e)return{order:"desc",orderby:"rating"}}return{order:"desc",orderby:"date_gmt"}})(e.orderby);return w||p||d||m||u||l?(0,h.jsx)(h.Fragment,{children:(0,h.jsx)(Re,{attributes:e,categoryIds:s,delayFunction:e=>(e=>{let t,o=null;const r=(...r)=>{o=r,t&&clearTimeout(t),t=setTimeout((()=>{t=null,o&&e(...o)}),400)};return r.flush=()=>{t&&o&&(e(...o),clearTimeout(t),t=null)},r.clear=()=>{t&&clearTimeout(t),t=null},r})(e),noReviewsPlaceholder:r,orderby:v,order:g,productId:i,reviewsToDisplay:n})}):(0,h.jsx)(c.Placeholder,{icon:t,label:o,children:(0,a.__)("The content for this block is hidden due to block settings.","woocommerce")})},Se=()=>(0,h.jsx)(c.Placeholder,{className:"wc-block-reviews-by-category",icon:(0,h.jsx)(s.A,{icon:i.A,className:"block-editor-block-icon"}),label:(0,a.__)("Reviews by Category","woocommerce"),children:(0,a.__)("This block lists reviews for products from selected categories. The selected categories do not have any reviews yet, but they will show up here when they do.","woocommerce")}),Ce=(e,t,o)=>(0,h.jsx)(n.BlockControls,{children:(0,h.jsx)(c.ToolbarGroup,{controls:[{icon:"edit",title:o,onClick:()=>t({editMode:!e}),isActive:e}]})}),Ne=(e,t)=>{const o=(0,P.getSetting)("showAvatars",!0),r=(0,P.getSetting)("reviewRatingsEnabled",!0);return(0,h.jsxs)(h.Fragment,{children:[(0,h.jsx)(c.ToggleControl,{label:(0,a.__)("Product rating","woocommerce"),checked:e.showReviewRating,onChange:()=>t({showReviewRating:!e.showReviewRating})}),e.showReviewRating&&!r&&(0,h.jsx)(c.Notice,{className:"wc-block-base-control-notice",isDismissible:!1,children:(0,d.createInterpolateElement)((0,a.__)("Product rating is disabled in your <a>store settings</a>.","woocommerce"),{a:(0,h.jsx)("a",{href:(0,P.getAdminLink)("admin.php?page=wc-settings&tab=products"),target:"_blank",rel:"noopener noreferrer"})})}),(0,h.jsx)(c.ToggleControl,{label:(0,a.__)("Reviewer name","woocommerce"),checked:e.showReviewerName,onChange:()=>t({showReviewerName:!e.showReviewerName})}),(0,h.jsx)(c.ToggleControl,{label:(0,a.__)("Image","woocommerce"),checked:e.showReviewImage,onChange:()=>t({showReviewImage:!e.showReviewImage})}),(0,h.jsx)(c.ToggleControl,{label:(0,a.__)("Review date","woocommerce"),checked:e.showReviewDate,onChange:()=>t({showReviewDate:!e.showReviewDate})}),(0,h.jsx)(c.ToggleControl,{label:(0,a.__)("Review content","woocommerce"),checked:e.showReviewContent,onChange:()=>t({showReviewContent:!e.showReviewContent})}),e.showReviewImage&&(0,h.jsxs)(h.Fragment,{children:[(0,h.jsxs)(c.__experimentalToggleGroupControl,{label:(0,a.__)("Review image","woocommerce"),isBlock:!0,value:e.imageType,onChange:e=>t({imageType:e}),children:[(0,h.jsx)(c.__experimentalToggleGroupControlOption,{value:"reviewer",label:(0,a.__)("Reviewer photo","woocommerce")}),(0,h.jsx)(c.__experimentalToggleGroupControlOption,{value:"product",label:(0,a.__)("Product","woocommerce")})]}),"reviewer"===e.imageType&&!o&&(0,h.jsx)(c.Notice,{className:"wc-block-base-control-notice",isDismissible:!1,children:(0,d.createInterpolateElement)((0,a.__)("Reviewer photo is disabled in your <a>site settings</a>.","woocommerce"),{a:(0,h.jsx)("a",{href:(0,P.getAdminLink)("options-discussion.php"),target:"_blank",rel:"noopener noreferrer"})})})]})]})},Te=(e,t)=>(0,h.jsxs)(h.Fragment,{children:[(0,h.jsx)(c.ToggleControl,{label:(0,a.__)("Order by","woocommerce"),checked:e.showOrderby,onChange:()=>t({showOrderby:!e.showOrderby})}),(0,h.jsx)(c.SelectControl,{label:(0,a.__)("Order Product Reviews by","woocommerce"),value:e.orderby,options:[{label:"Most recent",value:"most-recent"},{label:"Highest Rating",value:"highest-rating"},{label:"Lowest Rating",value:"lowest-rating"}],onChange:e=>t({orderby:e})}),(0,h.jsx)(c.RangeControl,{label:(0,a.__)("Starting Number of Reviews","woocommerce"),value:e.reviewsOnPageLoad,onChange:e=>t({reviewsOnPageLoad:e}),max:20,min:1}),(0,h.jsx)(c.ToggleControl,{label:(0,a.__)("Load more","woocommerce"),checked:e.showLoadMore,onChange:()=>t({showLoadMore:!e.showLoadMore})}),e.showLoadMore&&(0,h.jsx)(c.RangeControl,{label:(0,a.__)("Load More Reviews","woocommerce"),value:e.reviewsOnLoadMore,onChange:e=>t({reviewsOnLoadMore:e}),max:20,min:1})]}),Oe=(0,c.withSpokenMessages)((({attributes:e,debouncedSpeak:t,setAttributes:o})=>{const{editMode:r,categoryIds:l}=e;if(!l||r)return(0,h.jsxs)(c.Placeholder,{icon:(0,h.jsx)(s.A,{icon:i.A,className:"block-editor-block-icon"}),label:(0,a.__)("Reviews by Category","woocommerce"),className:"wc-block-reviews-by-category",children:[(0,a.__)("Show product reviews from specific categories.","woocommerce"),(0,h.jsxs)("div",{className:"wc-block-reviews__selection",children:[(0,h.jsx)(U,{selected:e.categoryIds,onChange:(e=[])=>{const t=e.map((({id:e})=>e));o({categoryIds:t})},showReviewCount:!0}),(0,h.jsx)(c.Button,{variant:"primary",onClick:()=>{o({editMode:!1}),t((0,a.__)("Now displaying a preview of the reviews for the products in the selected categories.","woocommerce"))},children:(0,a.__)("Done","woocommerce")})]})]});const d=(0,a.__)("Edit selected categories","woocommerce");return(0,h.jsxs)(h.Fragment,{children:[Ce(r,o,d),(0,h.jsxs)(n.InspectorControls,{children:[(0,h.jsx)(c.PanelBody,{title:(0,a.__)("Category","woocommerce"),initialOpen:!1,children:(0,h.jsx)(U,{selected:e.categoryIds,onChange:(e=[])=>{const t=e.map((({id:e})=>e));o({categoryIds:t})},isCompact:!0,showReviewCount:!0})}),(0,h.jsxs)(c.PanelBody,{title:(0,a.__)("Content","woocommerce"),children:[(0,h.jsx)(c.ToggleControl,{label:(0,a.__)("Product name","woocommerce"),checked:e.showProductName,onChange:()=>o({showProductName:!e.showProductName})}),Ne(e,o)]}),(0,h.jsx)(c.PanelBody,{title:(0,a.__)("List Settings","woocommerce"),children:Te(e,o)})]},"inspector"),(0,h.jsx)(ke,{attributes:e,icon:(0,h.jsx)(s.A,{icon:i.A,className:"block-editor-block-icon"}),name:(0,a.__)("Reviews by Category","woocommerce"),noReviewsPlaceholder:Se})]})}));o(4809);const Ee={attributes:{editMode:!1,imageType:"reviewer",orderby:"most-recent",reviewsOnLoadMore:10,reviewsOnPageLoad:10,showLoadMore:!0,showOrderby:!0,showReviewDate:!0,showReviewerName:!0,showReviewImage:!0,showReviewRating:!0,showReviewContent:!0,previewReviews:[{id:1,date_created:"2019-07-15T17:05:04",formatted_date_created:(0,a.__)("July 15, 2019","woocommerce"),date_created_gmt:"2019-07-15T15:05:04",product_id:0,product_name:(0,a.__)("WordPress Pennant","woocommerce"),product_permalink:"#",
/* translators: An example person name used for the block previews. */
reviewer:(0,a.__)("Alice","woocommerce"),review:`<p>${(0,a.__)("I bought this product last week and I'm very happy with it.","woocommerce")}</p>\n`,reviewer_avatar_urls:{48:I.defaultAvatar,96:I.defaultAvatar},rating:5,verified:!0},{id:2,date_created:"2019-07-12T12:39:39",formatted_date_created:(0,a.__)("July 12, 2019","woocommerce"),date_created_gmt:"2019-07-12T10:39:39",product_id:0,product_name:(0,a.__)("WordPress Pennant","woocommerce"),product_permalink:"#",
/* translators: An example person name used for the block previews. */
reviewer:(0,a.__)("Bob","woocommerce"),review:`<p>${(0,a.__)("This product is awesome, I love it!","woocommerce")}</p>\n`,reviewer_avatar_urls:{48:I.defaultAvatar,96:I.defaultAvatar},rating:null,verified:!1}]}},Le=JSON.parse('{"$schema":"https://schemas.wp.org/trunk/block.json","apiVersion":3,"name":"woocommerce/reviews-by-category","title":"Reviews by Category","category":"woocommerce","keywords":["WooCommerce"],"description":"Show product reviews from specific categories.","textdomain":"woocommerce","supports":{"html":false,"interactivity":{"clientNavigation":true},"color":{"background":false},"typography":{"fontSize":true}}}');(0,r.registerBlockType)(Le,{icon:{src:(0,h.jsx)(s.A,{icon:i.A,className:"wc-block-editor-components-block-icon"})},example:{...Ee,attributes:{...Ee.attributes,categoryIds:[1],showProductName:!0}},attributes:{editMode:{type:"boolean",default:!0},imageType:{type:"string",default:"reviewer"},orderby:{type:"string",default:"most-recent"},reviewsOnLoadMore:{type:"number",default:10},reviewsOnPageLoad:{type:"number",default:10},showLoadMore:{type:"boolean",default:!0},showOrderby:{type:"boolean",default:!0},showReviewDate:{type:"boolean",default:!0},showReviewerName:{type:"boolean",default:!0},showReviewImage:{type:"boolean",default:!0},showReviewRating:{type:"boolean",default:!0},showReviewContent:{type:"boolean",default:!0},previewReviews:{type:"array",default:null},categoryIds:{type:"array",default:[]},showProductName:{type:"boolean",default:!0}},edit:e=>{const t=(0,n.useBlockProps)();return(0,h.jsx)("div",{...t,children:(0,h.jsx)(Oe,{...e})})},save:({attributes:e})=>(0,h.jsx)("div",{...n.useBlockProps.save({className:ye(e)}),...fe(e)})})},6919:()=>{},7313:()=>{},5183:()=>{},6878:()=>{},4809:()=>{},3120:()=>{},6982:()=>{},1939:()=>{},5022:()=>{},1609:e=>{"use strict";e.exports=window.React},790:e=>{"use strict";e.exports=window.ReactJSXRuntime},9491:e=>{"use strict";e.exports=window.wp.compose},6087:e=>{"use strict";e.exports=window.wp.element},7723:e=>{"use strict";e.exports=window.wp.i18n},923:e=>{"use strict";e.exports=window.wp.isShallowEqual},5573:e=>{"use strict";e.exports=window.wp.primitives}},s={};function i(e){var t=s[e];if(void 0!==t)return t.exports;var o=s[e]={exports:{}};return r[e].call(o.exports,o,o.exports,i),o.exports}i.m=r,e=[],i.O=(t,o,r,s)=>{if(!o){var n=1/0;for(d=0;d<e.length;d++){for(var[o,r,s]=e[d],a=!0,c=0;c<o.length;c++)(!1&s||n>=s)&&Object.keys(i.O).every((e=>i.O[e](o[c])))?o.splice(c--,1):(a=!1,s<n&&(n=s));if(a){e.splice(d--,1);var l=r();void 0!==l&&(t=l)}}return t}s=s||0;for(var d=e.length;d>0&&e[d-1][2]>s;d--)e[d]=e[d-1];e[d]=[o,r,s]},i.n=e=>{var t=e&&e.__esModule?()=>e.default:()=>e;return i.d(t,{a:t}),t},o=Object.getPrototypeOf?e=>Object.getPrototypeOf(e):e=>e.__proto__,i.t=function(e,r){if(1&r&&(e=this(e)),8&r)return e;if("object"==typeof e&&e){if(4&r&&e.__esModule)return e;if(16&r&&"function"==typeof e.then)return e}var s=Object.create(null);i.r(s);var n={};t=t||[null,o({}),o([]),o(o)];for(var a=2&r&&e;"object"==typeof a&&!~t.indexOf(a);a=o(a))Object.getOwnPropertyNames(a).forEach((t=>n[t]=()=>e[t]));return n.default=()=>e,i.d(s,n),s},i.d=(e,t)=>{for(var o in t)i.o(t,o)&&!i.o(e,o)&&Object.defineProperty(e,o,{enumerable:!0,get:t[o]})},i.o=(e,t)=>Object.prototype.hasOwnProperty.call(e,t),i.r=e=>{"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})},i.j=6273,(()=>{var e={6273:0};i.O.j=t=>0===e[t];var t=(t,o)=>{var r,s,[n,a,c]=o,l=0;if(n.some((t=>0!==e[t]))){for(r in a)i.o(a,r)&&(i.m[r]=a[r]);if(c)var d=c(i)}for(t&&t(o);l<n.length;l++)s=n[l],i.o(e,s)&&e[s]&&e[s][0](),e[s]=0;return i.O(d)},o=globalThis.webpackChunkwebpackWcBlocksMainJsonp=globalThis.webpackChunkwebpackWcBlocksMainJsonp||[];o.forEach(t.bind(null,0)),o.push=t.bind(null,o.push.bind(o))})();var n=i.O(void 0,[94],(()=>i(1635)));n=i.O(n),((this.wc=this.wc||{}).blocks=this.wc.blocks||{})["reviews-by-category"]=n})();
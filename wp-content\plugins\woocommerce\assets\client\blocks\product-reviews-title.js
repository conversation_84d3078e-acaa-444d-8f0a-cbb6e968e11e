(()=>{var e,t,o,r={1830:(e,t,o)=>{"use strict";const r=window.wp.blocks;var n=o(3316);const i=JSON.parse('{"$schema":"https://schemas.wp.org/trunk/block.json","apiVersion":3,"name":"woocommerce/product-reviews-title","title":"Reviews Title","category":"woocommerce","ancestor":["woocommerce/product-reviews"],"description":"Displays a title with the number of reviews.","textdomain":"woocommerce","usesContext":["postId","postType"],"attributes":{"textAlign":{"type":"string"},"showProductTitle":{"type":"boolean","default":true},"showReviewsCount":{"type":"boolean","default":true},"level":{"type":"number","default":2},"levelOptions":{"type":"array"}},"supports":{"anchor":false,"align":true,"html":false,"__experimentalBorder":{"radius":true,"color":true,"width":true,"style":true},"color":{"gradients":true,"__experimentalDefaultControls":{"background":true,"text":true}},"spacing":{"margin":true,"padding":true},"typography":{"fontSize":true,"lineHeight":true,"__experimentalFontFamily":true,"__experimentalFontWeight":true,"__experimentalFontStyle":true,"__experimentalTextTransform":true,"__experimentalTextDecoration":true,"__experimentalLetterSpacing":true,"__experimentalDefaultControls":{"fontSize":true,"__experimentalFontFamily":true,"__experimentalFontStyle":true,"__experimentalFontWeight":true}},"interactivity":{"clientNavigation":true}}}');var s=o(4921),c=o(7723);const l=window.wp.coreData,a=window.wp.components;var u=o(6087);const p=window.wp.data,w=window.wc.data,m=window.wp.blockEditor;var d=o(790);o(7836),(0,r.registerBlockType)(i,{edit:function({attributes:{textAlign:e,showProductTitle:t,showReviewsCount:o,level:r,levelOptions:n},setAttributes:i,context:{postType:v,postId:_}}){const f="h"+r,[h,g]=(0,u.useState)(3),[x]=(0,l.useEntityProp)("postType",v,"title",_),b=void 0===_,y=(0,m.useBlockProps)({className:(0,s.A)({[`has-text-align-${e}`]:e})});(0,u.useEffect)((()=>{b?g(3):(0,p.resolveSelect)(w.reviewsStore).getReviewsTotalCount({product:[Number(_)]}).then((e=>{g(e)})).catch((()=>{g(0)}))}),[_,b]);const O=(0,d.jsxs)(m.BlockControls,{group:"block",children:[(0,d.jsx)(m.AlignmentControl,{value:e,onChange:e=>i({textAlign:e})}),(0,d.jsx)(m.HeadingLevelDropdown,{value:r,options:n,onChange:e=>i({level:e})})]}),j=(0,d.jsx)(m.InspectorControls,{children:(0,d.jsxs)(a.PanelBody,{title:(0,c.__)("Settings","woocommerce"),children:[(0,d.jsx)(a.ToggleControl,{__nextHasNoMarginBottom:!0,label:(0,c.__)("Show Product Title","woocommerce"),checked:t,onChange:e=>i({showProductTitle:e})}),(0,d.jsx)(a.ToggleControl,{__nextHasNoMarginBottom:!0,label:(0,c.__)("Show Reviews Count","woocommerce"),checked:o,onChange:e=>i({showReviewsCount:e})})]})}),k=function(e,t,o,r){return e&&o?1===t?(0,c.sprintf)(/* translators: %s: Product title. */ /* translators: %s: Product title. */
(0,c.__)("One review for %s","woocommerce"),r):(0,c.sprintf)(/* translators: 1: Number of comments, 2: Product title. */ /* translators: 1: Number of comments, 2: Product title. */
(0,c._n)("%1$s review for %2$s","%1$s reviews for %2$s",t,"woocommerce"),t,r):!e&&o?1===t?(0,c.sprintf)(/* translators: %s: Product title. */ /* translators: %s: Product title. */
(0,c.__)("Review for %s","woocommerce"),r):(0,c.sprintf)(/* translators: %s: Product title. */ /* translators: %s: Product title. */
(0,c.__)("Reviews for %s","woocommerce"),r):e&&!o?1===t?(0,c.__)("One review","woocommerce"):(0,c.sprintf)(/* translators: %s: Number of reviews. */ /* translators: %s: Number of reviews. */
(0,c._n)("%s review","%s reviews",t,"woocommerce"),t):1===t?(0,c.__)("Review","woocommerce"):(0,c.__)("Reviews","woocommerce")}(o,h,t,b?(0,c.__)("Product Title","woocommerce"):x);return(0,d.jsxs)(d.Fragment,{children:[O,j,(0,d.jsx)(f,{...y,children:k})]})},icon:n.A})},7836:()=>{},1609:e=>{"use strict";e.exports=window.React},790:e=>{"use strict";e.exports=window.ReactJSXRuntime},6087:e=>{"use strict";e.exports=window.wp.element},7723:e=>{"use strict";e.exports=window.wp.i18n},5573:e=>{"use strict";e.exports=window.wp.primitives}},n={};function i(e){var t=n[e];if(void 0!==t)return t.exports;var o=n[e]={exports:{}};return r[e].call(o.exports,o,o.exports,i),o.exports}i.m=r,e=[],i.O=(t,o,r,n)=>{if(!o){var s=1/0;for(u=0;u<e.length;u++){for(var[o,r,n]=e[u],c=!0,l=0;l<o.length;l++)(!1&n||s>=n)&&Object.keys(i.O).every((e=>i.O[e](o[l])))?o.splice(l--,1):(c=!1,n<s&&(s=n));if(c){e.splice(u--,1);var a=r();void 0!==a&&(t=a)}}return t}n=n||0;for(var u=e.length;u>0&&e[u-1][2]>n;u--)e[u]=e[u-1];e[u]=[o,r,n]},i.n=e=>{var t=e&&e.__esModule?()=>e.default:()=>e;return i.d(t,{a:t}),t},o=Object.getPrototypeOf?e=>Object.getPrototypeOf(e):e=>e.__proto__,i.t=function(e,r){if(1&r&&(e=this(e)),8&r)return e;if("object"==typeof e&&e){if(4&r&&e.__esModule)return e;if(16&r&&"function"==typeof e.then)return e}var n=Object.create(null);i.r(n);var s={};t=t||[null,o({}),o([]),o(o)];for(var c=2&r&&e;"object"==typeof c&&!~t.indexOf(c);c=o(c))Object.getOwnPropertyNames(c).forEach((t=>s[t]=()=>e[t]));return s.default=()=>e,i.d(n,s),n},i.d=(e,t)=>{for(var o in t)i.o(t,o)&&!i.o(e,o)&&Object.defineProperty(e,o,{enumerable:!0,get:t[o]})},i.o=(e,t)=>Object.prototype.hasOwnProperty.call(e,t),i.r=e=>{"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})},i.j=8613,(()=>{var e={8613:0};i.O.j=t=>0===e[t];var t=(t,o)=>{var r,n,[s,c,l]=o,a=0;if(s.some((t=>0!==e[t]))){for(r in c)i.o(c,r)&&(i.m[r]=c[r]);if(l)var u=l(i)}for(t&&t(o);a<s.length;a++)n=s[a],i.o(e,n)&&e[n]&&e[n][0](),e[n]=0;return i.O(u)},o=globalThis.webpackChunkwebpackWcBlocksMainJsonp=globalThis.webpackChunkwebpackWcBlocksMainJsonp||[];o.forEach(t.bind(null,0)),o.push=t.bind(null,o.push.bind(o))})();var s=i.O(void 0,[94],(()=>i(1830)));s=i.O(s),((this.wc=this.wc||{}).blocks=this.wc.blocks||{})["product-reviews-title"]=s})();
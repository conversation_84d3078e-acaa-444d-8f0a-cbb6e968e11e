<?php
/**
 * Plugin Name: Woo Attiva Assicurazione
 * Plugin URI: 
 * Description: Plugin per l'attivazione delle assicurazioni per prodotti in WooCommerce
 * Version: 1.0.0
 * Author: <PERSON> '<PERSON><PERSON><PERSON><PERSON>
 * Author URI: 
 * Text Domain: woo-attiva-assicurazione
 * Domain Path: /languages
 * Requires at least: 5.6
 * Requires PHP: 7.4
 * WC requires at least: 5.0
 * WC tested up to: 7.0
 *
 * @package Woo_Attiva_Assicurazione
 */

// Prevengo l'accesso diretto al file
if (!defined('ABSPATH')) {
    exit;
}

// Definisco costanti
define('WAA_PLUGIN_DIR', plugin_dir_path(__FILE__));
define('WAA_PLUGIN_URL', plugin_dir_url(__FILE__));
define('WAA_VERSION', '1.0.0');

// Include i file necessari
function waa_include_files() {
    include_once WAA_PLUGIN_DIR . 'includes/class-waa-db.php';
    include_once WAA_PLUGIN_DIR . 'includes/class-waa-admin.php';
    include_once WAA_PLUGIN_DIR . 'includes/class-waa-email.php';
}

// Controllo che WooCommerce sia attivo
function waa_check_woocommerce_active() {
    if (!in_array('woocommerce/woocommerce.php', apply_filters('active_plugins', get_option('active_plugins')))) {
        add_action('admin_notices', 'waa_woocommerce_required_notice');
        return false;
    }
    return true;
}

// Messaggio di errore se WooCommerce non è attivo
function waa_woocommerce_required_notice() {
    ?>
    <div class="error">
        <p><?php _e('Il plugin Woo Attiva Assicurazione richiede WooCommerce per funzionare correttamente!', 'woo-attiva-assicurazione'); ?></p>
    </div>
    <?php
}

// Inizializzazione del plugin
function waa_init() {
    if (!waa_check_woocommerce_active()) {
        return;
    }
    
    waa_include_files();
    
    // Inizializzo le classi
    WAA_Admin::init();
    WAA_Email::init();
    
    // Registro gli asset
    add_action('admin_enqueue_scripts', 'waa_enqueue_admin_assets');
}

// Funzione di attivazione del plugin
function waa_activate_plugin() {
    // Includo i file necessari per utilizzare dbDelta
    require_once(ABSPATH . 'wp-admin/includes/upgrade.php');
    
    // Includo la classe del database
    waa_include_files();
    
    // Creo la tabella del database
    WAA_DB::create_tables();
}

// Registro l'hook di attivazione
register_activation_hook(__FILE__, 'waa_activate_plugin');

// Inizializzo il plugin quando WordPress è pronto
add_action('plugins_loaded', 'waa_init');

// Carico gli asset admin
function waa_enqueue_admin_assets($hook) {
    // Carico gli asset solo nella pagina dell'ordine
    $screen = get_current_screen();
    if ($screen->id == 'shop_order') {
        wp_enqueue_style('waa-admin-style', WAA_PLUGIN_URL . 'assets/css/admin.css', array(), WAA_VERSION);
        wp_enqueue_script('waa-admin-script', WAA_PLUGIN_URL . 'assets/js/admin.js', array('jquery'), WAA_VERSION, true);
        
        // Aggiungo le variabili JS
        wp_localize_script('waa-admin-script', 'waa_params', array(
            'ajax_url' => admin_url('admin-ajax.php'),
            'nonce' => wp_create_nonce('waa-admin-nonce')
        ));
    }
} 
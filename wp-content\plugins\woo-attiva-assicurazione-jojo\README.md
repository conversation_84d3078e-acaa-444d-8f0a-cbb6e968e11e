# WooCommerce Attiva Assicurazione

Plugin per WooCommerce che consente la gestione e l'attivazione delle assicurazioni per prodotti venduti.

## Descrizione

Questo plugin permette agli amministratori di WooCommerce di gestire le assicurazioni associate ai prodotti venduti. Le funzionalità principali includono:

- Registrazione delle assicurazioni per prodotti appartenenti alla categoria "Assicurazioni"
- Gestione dello stato delle assicurazioni (registrata, utilizzata, scaduta)
- Invio di email automatiche al cliente quando un'assicurazione viene registrata o utilizzata
- Tracciamento di dati importanti come IMEI, data di attivazione e motivo di utilizzo

## Requisiti

- WordPress 5.6 o superiore
- WooCommerce 5.0 o superiore
- PHP 7.4 o superiore

## Installazione

1. Carica la cartella `woo-attiva-assicurazione-jojo` nella directory `/wp-content/plugins/` del tuo sito WordPress
2. Attiva il plugin dalla pagina 'Plugin' in WordPress
3. Assicurati di avere creato una categoria di prodotti denominata "Assicurazioni"

## Utilizzo

### Configurazione Iniziale

1. Crea una categoria di prodotti chiamata "Assicurazioni"
2. Assegna i prodotti assicurativi a questa categoria

### Registrazione di un'Assicurazione

1. Vai alla pagina di dettaglio dell'ordine nell'admin di WooCommerce
2. Se l'ordine contiene prodotti della categoria "Assicurazioni", vedrai una sezione "Gestione Assicurazioni"
3. Compila il form con il prodotto, l'IMEI e la data di attivazione
4. Clicca su "Registra Assicurazione"
5. Una email di conferma verrà inviata automaticamente al cliente

### Utilizzo di un'Assicurazione

1. Vai alla pagina di dettaglio dell'ordine nell'admin di WooCommerce
2. Trova l'assicurazione registrata e compila il form di utilizzo con la data e la motivazione
3. Clicca su "Utilizza Assicurazione"
4. Una email di notifica verrà inviata automaticamente al cliente

## Supporto

Per supporto o segnalazioni di bug, contattare l'autore del plugin.

## Changelog

### 1.0.0
- Versione iniziale del plugin 